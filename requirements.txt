# 基础环境
# python>=3.8

# 爬虫相关
selenium>=4.1.0
webdriver-manager>=3.5.2
requests>=2.27.1
beautifulsoup4>=4.10.0
lxml>=4.8.0

# 数据处理与分析
pandas>=1.4.1
numpy>=1.22.3
scipy>=1.8.0
scikit-learn>=1.0.2

# 数据库
mysqlclient>=2.1.0
SQLAlchemy>=1.4.32

# 数据可视化
matplotlib>=3.5.1
seaborn>=0.11.2
pyecharts>=1.9.0

# Web框架
flask>=2.0.3
flask-sqlalchemy>=2.5.1
flask-login>=0.5.0
flask-wtf>=1.0.0

# 工具库
tqdm>=4.63.0
python-dotenv>=0.19.2
fake-useragent>=0.1.11
retrying>=1.3.3