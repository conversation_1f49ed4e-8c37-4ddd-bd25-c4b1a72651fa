# -*- coding: utf-8 -*-
"""
数据分析模块，用于分析电商数据
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from sqlalchemy import func, desc, and_, or_

from .models import Product, Category, Shop, PriceHistory, SalesHistory
from .db_connection import get_db_session, close_db_session

# 配置日志
logger = logging.getLogger(__name__)

class DataAnalyzer:
    """
    数据分析类，提供各种数据分析功能
    """
    
    def __init__(self):
        """
        初始化数据分析器
        """
        self.session = get_db_session()
    
    def __del__(self):
        """
        析构函数，确保会话被关闭
        """
        close_db_session()
    
    def get_product_count(self, category_name=None, start_date=None, end_date=None):
        """
        获取产品数量
        
        Args:
            category_name: 分类名称，如果为None则获取所有分类
            start_date: 开始日期，如果为None则不限制
            end_date: 结束日期，如果为None则不限制
            
        Returns:
            int: 产品数量
        """
        query = self.session.query(Product)
        
        # 添加分类过滤
        if category_name:
            query = query.join(Category).filter(Category.name == category_name)
        
        # 添加日期过滤
        if start_date:
            query = query.filter(Product.crawl_time >= start_date)
        if end_date:
            query = query.filter(Product.crawl_time <= end_date)
        
        return query.count()
    
    def get_category_stats(self):
        """
        获取分类统计信息
        
        Returns:
            list: 分类统计信息列表，每个元素包含分类名称、产品数量、平均价格、平均销量
        """
        result = self.session.query(
            Category.name,
            func.count(Product.id).label('product_count'),
            func.avg(Product.price).label('avg_price'),
            func.avg(Product.sales).label('avg_sales')
        ).join(Product, Category.id == Product.category_id)\
         .group_by(Category.name)\
         .order_by(desc('product_count'))\
         .all()
        
        return [
            {
                'name': item[0],
                'product_count': item[1],
                'avg_price': round(item[2], 2) if item[2] else 0,
                'avg_sales': round(item[3], 2) if item[3] else 0
            }
            for item in result
        ]
    
    def get_price_range_distribution(self, category_name=None):
        """
        获取价格区间分布
        
        Args:
            category_name: 分类名称，如果为None则获取所有分类
            
        Returns:
            dict: 价格区间分布，键为价格区间，值为产品数量
        """
        query = self.session.query(Product.price)
        
        # 添加分类过滤
        if category_name:
            query = query.join(Category).filter(Category.name == category_name)
        
        # 获取所有价格
        prices = [p[0] for p in query.all() if p[0] is not None]
        
        if not prices:
            return {}
        
        # 创建价格区间
        max_price = max(prices)
        if max_price <= 100:
            ranges = [(0, 20), (20, 40), (40, 60), (60, 80), (80, 100), (100, float('inf'))]
        elif max_price <= 1000:
            ranges = [(0, 100), (100, 300), (300, 500), (500, 700), (700, 1000), (1000, float('inf'))]
        elif max_price <= 10000:
            ranges = [(0, 1000), (1000, 3000), (3000, 5000), (5000, 7000), (7000, 10000), (10000, float('inf'))]
        else:
            ranges = [(0, 5000), (5000, 10000), (10000, 20000), (20000, 30000), (30000, 50000), (50000, float('inf'))]
        
        # 统计每个区间的产品数量
        distribution = {}
        for start, end in ranges:
            if end == float('inf'):
                label = f"{start}+"
            else:
                label = f"{start}-{end}"
            
            count = sum(1 for p in prices if start <= p < end)
            distribution[label] = count
        
        return distribution
    
    def get_top_products(self, category_name=None, limit=10, order_by='sales'):
        """
        获取热门产品
        
        Args:
            category_name: 分类名称，如果为None则获取所有分类
            limit: 返回的产品数量
            order_by: 排序字段，可选值：sales, price, rating
            
        Returns:
            list: 产品列表
        """
        query = self.session.query(Product)
        
        # 添加分类过滤
        if category_name:
            query = query.join(Category).filter(Category.name == category_name)
        
        # 添加排序
        if order_by == 'sales':
            query = query.order_by(desc(Product.sales))
        elif order_by == 'price':
            query = query.order_by(desc(Product.price))
        elif order_by == 'rating':
            query = query.order_by(desc(Product.rating))
        
        # 获取结果
        products = query.limit(limit).all()
        
        return [
            {
                'id': p.id,
                'product_id': p.product_id,
                'title': p.title,
                'price': p.price,
                'sales': p.sales,
                'rating': p.rating,
                'shop_name': p.shop.name if p.shop else None,
                'category_name': p.category.name if p.category else None
            }
            for p in products
        ]
    
    def get_price_trend(self, product_id=None, category_name=None, days=30):
        """
        获取价格趋势
        
        Args:
            product_id: 产品ID，如果为None则获取分类平均价格
            category_name: 分类名称，如果为None则获取所有分类
            days: 天数
            
        Returns:
            dict: 价格趋势，键为日期，值为价格
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        if product_id:
            # 获取单个产品的价格趋势
            query = self.session.query(
                func.date(PriceHistory.date).label('date'),
                func.avg(PriceHistory.price).label('price')
            ).filter(
                PriceHistory.product_id == product_id,
                PriceHistory.date >= start_date,
                PriceHistory.date <= end_date
            ).group_by(func.date(PriceHistory.date))\
             .order_by('date')
        else:
            # 获取分类平均价格趋势
            query = self.session.query(
                func.date(PriceHistory.date).label('date'),
                func.avg(PriceHistory.price).label('price')
            ).join(Product, Product.product_id == PriceHistory.product_id)
            
            # 添加分类过滤
            if category_name:
                query = query.join(Category, Product.category_id == Category.id)\
                       .filter(Category.name == category_name)
            
            query = query.filter(
                PriceHistory.date >= start_date,
                PriceHistory.date <= end_date
            ).group_by(func.date(PriceHistory.date))\
             .order_by('date')
        
        # 获取结果
        result = query.all()
        
        # 转换为字典
        trend = {}
        for date, price in result:
            trend[date.strftime('%Y-%m-%d')] = round(price, 2)
        
        return trend
    
    def get_sales_trend(self, product_id=None, category_name=None, days=30):
        """
        获取销量趋势
        
        Args:
            product_id: 产品ID，如果为None则获取分类平均销量
            category_name: 分类名称，如果为None则获取所有分类
            days: 天数
            
        Returns:
            dict: 销量趋势，键为日期，值为销量
        """
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        if product_id:
            # 获取单个产品的销量趋势
            query = self.session.query(
                func.date(SalesHistory.date).label('date'),
                func.avg(SalesHistory.sales).label('sales')
            ).filter(
                SalesHistory.product_id == product_id,
                SalesHistory.date >= start_date,
                SalesHistory.date <= end_date
            ).group_by(func.date(SalesHistory.date))\
             .order_by('date')
        else:
            # 获取分类平均销量趋势
            query = self.session.query(
                func.date(SalesHistory.date).label('date'),
                func.avg(SalesHistory.sales).label('sales')
            ).join(Product, Product.product_id == SalesHistory.product_id)
            
            # 添加分类过滤
            if category_name:
                query = query.join(Category, Product.category_id == Category.id)\
                       .filter(Category.name == category_name)
            
            query = query.filter(
                SalesHistory.date >= start_date,
                SalesHistory.date <= end_date
            ).group_by(func.date(SalesHistory.date))\
             .order_by('date')
        
        # 获取结果
        result = query.all()
        
        # 转换为字典
        trend = {}
        for date, sales in result:
            trend[date.strftime('%Y-%m-%d')] = round(sales, 2)
        
        return trend
    
    def get_shop_stats(self, limit=10):
        """
        获取店铺统计信息
        
        Args:
            limit: 返回的店铺数量
            
        Returns:
            list: 店铺统计信息列表
        """
        result = self.session.query(
            Shop.name,
            func.count(Product.id).label('product_count'),
            func.avg(Product.price).label('avg_price'),
            func.avg(Product.sales).label('avg_sales'),
            func.avg(Product.rating).label('avg_rating')
        ).join(Product, Shop.id == Product.shop_id)\
         .group_by(Shop.name)\
         .order_by(desc('product_count'))\
         .limit(limit)\
         .all()
        
        return [
            {
                'name': item[0],
                'product_count': item[1],
                'avg_price': round(item[2], 2) if item[2] else 0,
                'avg_sales': round(item[3], 2) if item[3] else 0,
                'avg_rating': round(item[4], 2) if item[4] else 0
            }
            for item in result
        ]
    
    def get_location_stats(self, limit=None):
        """
        获取地区统计信息
        
        Args:
            limit: 返回的地区数量，如果为None则返回所有地区
            
        Returns:
            list: 地区统计信息列表
        """
        query = self.session.query(
            Product.location,
            func.count(Product.id).label('product_count'),
            func.avg(Product.price).label('avg_price'),
            func.avg(Product.sales).label('avg_sales')
        ).filter(Product.location != None)\
         .group_by(Product.location)\
         .order_by(desc('product_count'))
         
        # 添加数量限制
        if limit:
            query = query.limit(limit)
            
        result = query.all()
        
        return [
            {
                'location': item[0],
                'product_count': item[1],
                'avg_price': round(item[2], 2) if item[2] else 0,
                'avg_sales': round(item[3], 2) if item[3] else 0
            }
            for item in result
        ]
    
    def get_summary_stats(self, category_name=None):
        """
        获取汇总统计信息
        
        Args:
            category_name: 分类名称，如果为None则获取所有分类
            
        Returns:
            dict: 汇总统计信息
        """
        query = self.session.query(
            func.count(Product.id).label('product_count'),
            func.avg(Product.price).label('avg_price'),
            func.min(Product.price).label('min_price'),
            func.max(Product.price).label('max_price'),
            func.avg(Product.sales).label('avg_sales'),
            func.sum(Product.sales).label('total_sales'),
            func.avg(Product.rating).label('avg_rating'),
            func.count(func.distinct(Shop.id)).label('shop_count')
        ).join(Shop, Product.shop_id == Shop.id, isouter=True)
        
        # 添加分类过滤
        if category_name:
            query = query.join(Category, Product.category_id == Category.id)\
                   .filter(Category.name == category_name)
        
        result = query.first()
        
        # 获取30天前的产品数量，用于计算增长率
        thirty_days_ago = datetime.now() - timedelta(days=30)
        old_query = self.session.query(func.count(Product.id))\
                    .filter(Product.created_at < thirty_days_ago)
        
        # 添加分类过滤
        if category_name:
            old_query = old_query.join(Category, Product.category_id == Category.id)\
                       .filter(Category.name == category_name)
        
        old_count = old_query.scalar() or 0
        current_count = result[0] or 0
        
        # 计算增长率
        if old_count > 0:
            growth_rate = (current_count - old_count) / old_count * 100
        else:
            growth_rate = 100 if current_count > 0 else 0
        
        return {
            'product_count': result[0] or 0,
            'avg_price': round(result[1], 2) if result[1] else 0,
            'min_price': round(result[2], 2) if result[2] else 0,
            'max_price': round(result[3], 2) if result[3] else 0,
            'avg_sales': round(result[4], 2) if result[4] else 0,
            'total_sales': result[5] or 0,
            'avg_rating': round(result[6], 2) if result[6] else 0,
            'shop_count': result[7] or 0,
            'growth_rate': round(growth_rate, 2)
        }
        
    def get_price_sales_scatter(self, category_name=None, limit=100):
        """
        获取价格-销量散点图数据
        
        Args:
            category_name: 分类名称，如果为None则获取所有分类
            limit: 返回的产品数量
            
        Returns:
            list: 散点图数据列表，每个元素包含产品ID、标题、价格、销量
        """
        query = self.session.query(
            Product.id,
            Product.product_id,
            Product.title,
            Product.price,
            Product.sales,
            Category.name.label('category_name')
        ).join(Category, Product.category_id == Category.id)
        
        # 添加分类过滤
        if category_name:
            query = query.filter(Category.name == category_name)
        
        # 获取结果
        products = query.limit(limit).all()
        
        return [
            {
                'id': p.id,
                'product_id': p.product_id,
                'title': p.title,
                'price': float(p.price) if p.price else 0,
                'sales': p.sales,
                'category': p.category_name
            }
            for p in products
        ]

# 导出分析器实例
def get_analyzer():
    """
    获取数据分析器实例
    
    Returns:
        DataAnalyzer: 数据分析器实例
    """
    return DataAnalyzer()