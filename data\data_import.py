# -*- coding: utf-8 -*-
"""
数据导入模块，用于将爬取的JSON数据导入到MySQL数据库
"""

import os
import json
import logging
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm.exc import NoResultFound

from .db_connection import get_db_session, close_db_session
from .models import Category, Product, Shop, RawData, PriceHistory, SalesHistory

# 配置日志
logger = logging.getLogger(__name__)

class DataImporter:
    """
    数据导入类，用于将爬取的JSON数据导入到MySQL数据库
    """
    
    def __init__(self):
        """
        初始化数据导入器
        """
        self.session = get_db_session()
    
    def close(self):
        """
        关闭数据库会话
        """
        close_db_session()
    
    def import_from_file(self, file_path):
        """
        从JSON文件导入数据
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            tuple: (成功导入数量, 失败数量)
        """
        try:
            # 从文件名中提取分类名称
            file_name = os.path.basename(file_path)
            category_name = file_name.split('_')[0] if '_' in file_name else None
            
            # 读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                products_data = json.load(f)
            
            logger.info(f"从文件 {file_path} 中读取了 {len(products_data)} 条产品数据")
            
            # 导入数据
            return self.import_products(products_data, category_name)
            
        except Exception as e:
            logger.error(f"从文件 {file_path} 导入数据失败: {str(e)}")
            return 0, 0
    
    def import_from_directory(self, directory):
        """
        从目录导入所有JSON文件
        
        Args:
            directory: 目录路径
            
        Returns:
            tuple: (成功导入数量, 失败数量)
        """
        total_success = 0
        total_failed = 0
        
        try:
            # 遍历目录中的所有JSON文件
            for file_name in os.listdir(directory):
                if file_name.endswith('.json'):
                    file_path = os.path.join(directory, file_name)
                    success, failed = self.import_from_file(file_path)
                    total_success += success
                    total_failed += failed
            
            logger.info(f"从目录 {directory} 中导入了 {total_success} 条产品数据，失败 {total_failed} 条")
            return total_success, total_failed
            
        except Exception as e:
            logger.error(f"从目录 {directory} 导入数据失败: {str(e)}")
            return total_success, total_failed
    
    def import_products(self, products_data, category_name=None):
        """
        导入产品数据
        
        Args:
            products_data: 产品数据列表
            category_name: 分类名称
            
        Returns:
            tuple: (成功导入数量, 失败数量)
        """
        success_count = 0
        failed_count = 0
        
        # 获取或创建分类
        category = None
        if category_name:
            category = self._get_or_create_category(category_name)
        
        # 遍历产品数据
        for product_data in products_data:
            try:
                # 导入产品
                self._import_product(product_data, category)
                success_count += 1
            except Exception as e:
                logger.error(f"导入产品 {product_data.get('product_id', 'unknown')} 失败: {str(e)}")
                failed_count += 1
        
        # 提交事务
        try:
            self.session.commit()
            logger.info(f"成功导入 {success_count} 条产品数据，失败 {failed_count} 条")
        except Exception as e:
            self.session.rollback()
            logger.error(f"提交事务失败: {str(e)}")
            return 0, success_count + failed_count
        
        return success_count, failed_count
    
    def _get_or_create_category(self, category_name):
        """
        获取或创建分类
        
        Args:
            category_name: 分类名称
            
        Returns:
            Category: 分类对象
        """
        try:
            # 查询分类
            category = self.session.query(Category).filter(Category.name == category_name).one()
            return category
        except NoResultFound:
            # 创建分类
            category = Category(name=category_name)
            self.session.add(category)
            self.session.flush()
            logger.info(f"创建了新分类: {category_name}")
            return category
    
    def _get_or_create_shop(self, shop_name, shop_url=None):
        """
        获取或创建店铺
        
        Args:
            shop_name: 店铺名称
            shop_url: 店铺URL
            
        Returns:
            Shop: 店铺对象
        """
        if not shop_name:
            return None
            
        try:
            # 查询店铺
            shop = self.session.query(Shop).filter(Shop.name == shop_name).one()
            return shop
        except NoResultFound:
            # 创建店铺
            shop = Shop(name=shop_name, url=shop_url)
            self.session.add(shop)
            self.session.flush()
            logger.debug(f"创建了新店铺: {shop_name}")
            return shop
    
    def _import_product(self, product_data, category=None):
        """
        导入单个产品数据
        
        Args:
            product_data: 产品数据字典
            category: 分类对象
            
        Returns:
            Product: 产品对象
        """
        product_id = product_data.get('product_id')
        if not product_id:
            raise ValueError("产品ID不能为空")
        
        # 检查产品是否已存在
        existing_product = self.session.query(Product).filter(Product.product_id == product_id).first()
        
        if existing_product:
            # 更新现有产品
            return self._update_product(existing_product, product_data, category)
        else:
            # 创建新产品
            return self._create_product(product_data, category)
    
    def _create_product(self, product_data, category=None):
        """
        创建新产品
        
        Args:
            product_data: 产品数据字典
            category: 分类对象
            
        Returns:
            Product: 产品对象
        """
        # 获取或创建店铺
        shop = self._get_or_create_shop(
            shop_name=product_data.get('shop_name'),
            shop_url=product_data.get('shop_url')
        )
        
        # 处理价格
        price_str = product_data.get('price', '0')
        price = float(price_str) if price_str and price_str.replace('.', '').isdigit() else 0.0
        
        # 处理销量
        sales_str = product_data.get('sales', '0')
        sales = int(sales_str) if sales_str and sales_str.isdigit() else 0
        
        # 处理评论数
        review_count_str = product_data.get('review_count', '0')
        review_count = int(review_count_str) if review_count_str and review_count_str.isdigit() else 0
        
        # 处理评分
        rating_str = product_data.get('rating', '0')
        rating = float(rating_str) if rating_str and rating_str.replace('.', '').isdigit() else 0.0
        
        # 处理爬取时间
        crawl_time_str = product_data.get('crawl_time')
        crawl_time = datetime.strptime(crawl_time_str, "%Y-%m-%d %H:%M:%S") if crawl_time_str else datetime.now()
        
        # 创建产品对象
        product = Product(
            product_id=product_data.get('product_id'),
            title=product_data.get('title'),
            price=price,
            sales=sales,
            detail_url=product_data.get('detail_url'),
            location=product_data.get('location'),
            rating=rating,
            review_count=review_count,
            keyword=product_data.get('keyword'),
            attributes=product_data.get('attributes'),
            crawl_time=crawl_time,
            category=category,
            shop_id=shop.id if shop else None
        )
        
        # 添加到会话
        self.session.add(product)
        
        # 保存原始数据
        raw_data = RawData(
            product_id=product_data.get('product_id'),
            data=json.dumps(product_data, ensure_ascii=False),
            crawl_time=crawl_time
        )
        self.session.add(raw_data)
        
        # 保存价格历史
        if price > 0:
            price_history = PriceHistory(
                product_id=product_data.get('product_id'),
                price=price,
                date=crawl_time
            )
            self.session.add(price_history)
        
        # 保存销量历史
        if sales > 0:
            sales_history = SalesHistory(
                product_id=product_data.get('product_id'),
                sales=sales,
                date=crawl_time
            )
            self.session.add(sales_history)
        
        self.session.flush()
        return product
    
    def _update_product(self, product, product_data, category=None):
        """
        更新现有产品
        
        Args:
            product: 产品对象
            product_data: 产品数据字典
            category: 分类对象
            
        Returns:
            Product: 产品对象
        """
        # 处理价格
        price_str = product_data.get('price', '0')
        price = float(price_str) if price_str and price_str.replace('.', '').isdigit() else 0.0
        
        # 处理销量
        sales_str = product_data.get('sales', '0')
        sales = int(sales_str) if sales_str and sales_str.isdigit() else 0
        
        # 处理评论数
        review_count_str = product_data.get('review_count', '0')
        review_count = int(review_count_str) if review_count_str and review_count_str.isdigit() else 0
        
        # 处理评分
        rating_str = product_data.get('rating', '0')
        rating = float(rating_str) if rating_str and rating_str.replace('.', '').isdigit() else 0.0
        
        # 处理爬取时间
        crawl_time_str = product_data.get('crawl_time')
        crawl_time = datetime.strptime(crawl_time_str, "%Y-%m-%d %H:%M:%S") if crawl_time_str else datetime.now()
        
        # 更新产品信息
        if product_data.get('title'):
            product.title = product_data.get('title')
        if price > 0:
            product.price = price
        if sales > 0:
            product.sales = sales
        if product_data.get('detail_url'):
            product.detail_url = product_data.get('detail_url')
        if product_data.get('location'):
            product.location = product_data.get('location')
        if rating > 0:
            product.rating = rating
        if review_count > 0:
            product.review_count = review_count
        if product_data.get('keyword'):
            product.keyword = product_data.get('keyword')
        if product_data.get('attributes'):
            product.attributes = product_data.get('attributes')
        product.crawl_time = crawl_time
        
        # 更新分类
        if category and not product.category:
            product.category = category
        
        # 更新店铺
        if product_data.get('shop_name') and not product.shop_id:
            shop = self._get_or_create_shop(
                shop_name=product_data.get('shop_name'),
                shop_url=product_data.get('shop_url')
            )
            product.shop_id = shop.id if shop else None
        
        # 保存原始数据
        raw_data = RawData(
            product_id=product_data.get('product_id'),
            data=json.dumps(product_data, ensure_ascii=False),
            crawl_time=crawl_time
        )
        self.session.add(raw_data)
        
        # 保存价格历史（如果价格变化）
        if price > 0 and price != product.price:
            price_history = PriceHistory(
                product_id=product_data.get('product_id'),
                price=price,
                date=crawl_time
            )
            self.session.add(price_history)
        
        # 保存销量历史（如果销量变化）
        if sales > 0 and sales != product.sales:
            sales_history = SalesHistory(
                product_id=product_data.get('product_id'),
                sales=sales,
                date=crawl_time
            )
            self.session.add(sales_history)
        
        self.session.flush()
        return product

# 导出主函数
def import_data(source, category_name=None):
    """
    导入数据的主函数
    
    Args:
        source: 数据源，可以是文件路径或目录路径
        category_name: 分类名称，如果为None则从文件名中提取
        
    Returns:
        tuple: (成功导入数量, 失败数量)
    """
    importer = DataImporter()
    try:
        if os.path.isdir(source):
            return importer.import_from_directory(source)
        elif os.path.isfile(source):
            return importer.import_from_file(source)
        else:
            logger.error(f"数据源 {source} 不存在")
            return 0, 0
    finally:
        importer.close()

# 命令行入口
if __name__ == "__main__":
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="淘宝电子产品数据导入工具")
    parser.add_argument("source", help="数据源，可以是JSON文件路径或包含JSON文件的目录路径")
    parser.add_argument("--category", help="分类名称，如果不指定则从文件名中提取")
    
    args = parser.parse_args()
    
    # 导入数据
    success, failed = import_data(args.source, args.category)
    print(f"导入完成，成功: {success}，失败: {failed}")