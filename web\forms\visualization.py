# -*- coding: utf-8 -*-
"""
可视化表单模块，用于处理数据可视化相关的表单
"""

from flask_wtf import FlaskForm
from wtforms import SelectField, DateField, SubmitField, IntegerField, StringField
from wtforms.validators import DataRequired, Optional, NumberRange

from data.models import Category


class ChartFilterForm(FlaskForm):
    """
    图表过滤表单
    用于过滤图表数据
    """
    category_id = SelectField('分类', coerce=int, validators=[Optional()])
    chart_type = SelectField('图表类型', validators=[DataRequired()])
    output_format = SelectField('输出格式', choices=[
        ('png', 'PNG图片'),
        ('jpg', 'JPG图片'),
        ('svg', 'SVG矢量图'),
        ('pdf', 'PDF文档'),
        ('html', 'HTML交互式图表'),
    ], default='png')
    submit = SubmitField('生成图表')
    
    def __init__(self, *args, **kwargs):
        super(ChartFilterForm, self).__init__(*args, **kwargs)
        # 动态加载分类选项
        self.category_id.choices = [(0, '所有分类')] + [
            (category.id, category.name) for category in Category.query.all()
        ]


class BasicChartForm(ChartFilterForm):
    """
    基础图表表单
    用于基础图表参数设置
    """
    def __init__(self, *args, **kwargs):
        super(BasicChartForm, self).__init__(*args, **kwargs)
        # 设置图表类型选项
        self.chart_type.choices = [
            ('category_distribution', '分类分布'),
            ('price_distribution', '价格分布'),
            ('sales_distribution', '销量分布'),
            ('top_products', '热门产品排行'),
            ('top_shops', '热门店铺排行'),
            ('location_distribution', '地区分布'),
            ('keyword_distribution', '关键词分布'),
            ('price_sales_correlation', '价格与销量相关性'),
        ]


class AdvancedChartForm(ChartFilterForm):
    """
    高级图表表单
    用于高级图表参数设置
    """
    start_date = DateField('开始日期', format='%Y-%m-%d', validators=[Optional()])
    end_date = DateField('结束日期', format='%Y-%m-%d', validators=[Optional()])
    top_n = IntegerField('显示前N个结果', validators=[Optional(), NumberRange(min=1, max=100)], default=10)
    
    def __init__(self, *args, **kwargs):
        super(AdvancedChartForm, self).__init__(*args, **kwargs)
        # 设置图表类型选项
        self.chart_type.choices = [
            ('price_trend', '价格趋势'),
            ('sales_trend', '销量趋势'),
            ('brand_analysis', '品牌分析'),
            ('seasonality', '季节性分析'),
            ('competition', '竞争对手分析'),
            ('keyword_cloud', '关键词词云'),
            ('sentiment_analysis', '情感分析'),
        ]
        # 设置默认输出格式为HTML（交互式图表）
        self.output_format.default = 'html'


class DashboardForm(FlaskForm):
    """
    仪表盘表单
    用于仪表盘参数设置
    """
    category_id = SelectField('分类', coerce=int, validators=[Optional()])
    time_range = SelectField('时间范围', choices=[
        ('7d', '最近7天'),
        ('30d', '最近30天'),
        ('90d', '最近90天'),
        ('180d', '最近180天'),
        ('365d', '最近一年'),
        ('all', '全部时间'),
    ], default='30d')
    submit = SubmitField('更新仪表盘')
    
    def __init__(self, *args, **kwargs):
        super(DashboardForm, self).__init__(*args, **kwargs)
        # 动态加载分类选项
        self.category_id.choices = [(0, '所有分类')] + [
            (category.id, category.name) for category in Category.query.all()
        ]