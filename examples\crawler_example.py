# -*- coding: utf-8 -*-
"""
爬虫模块使用示例

该脚本展示了如何使用爬虫模块爬取淘宝电子产品数据
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入爬虫模块
from crawler import TaobaoCrawler, ELECTRONIC_CATEGORIES, save_to_json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("crawler_example.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def crawl_single_category(category_name, pages=1, get_details=False):
    """
    爬取单个分类的产品数据
    
    Args:
        category_name: 分类名称
        pages: 每个关键词爬取的页数
        get_details: 是否获取详细信息
    """
    # 查找对应的分类配置
    category = None
    for cat in ELECTRONIC_CATEGORIES:
        if cat["name"] == category_name:
            category = cat
            break
    
    if not category:
        logger.error(f"未找到分类: {category_name}")
        return
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/raw")
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建爬虫实例
    crawler = TaobaoCrawler()
    
    try:
        logger.info(f"开始爬取分类: {category_name}")
        
        # 爬取数据
        products = crawler.crawl(
            keywords=category["keywords"],
            pages_per_keyword=pages,
            get_details=get_details
        )
        
        # 保存数据
        if products:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(output_dir, f"{category_name}_{timestamp}.json")
            save_to_json(products, filename)
            logger.info(f"成功爬取 {len(products)} 个产品数据并保存到 {filename}")
        else:
            logger.warning(f"未爬取到任何产品数据")
            
    except Exception as e:
        logger.error(f"爬取过程中发生错误: {str(e)}")
    finally:
        # 关闭爬虫
        crawler.close()


def main():
    """
    主函数
    """
    # 示例：爬取手机分类的数据
    crawl_single_category("手机", pages=1, get_details=False)
    
    # 如果要爬取其他分类，可以取消下面的注释并修改参数
    # crawl_single_category("笔记本电脑", pages=1, get_details=False)
    # crawl_single_category("智能手表", pages=1, get_details=False)


if __name__ == "__main__":
    main()
