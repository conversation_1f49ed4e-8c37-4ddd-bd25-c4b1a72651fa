# -*- coding: utf-8 -*-
"""
产品表单模块，用于处理产品相关的表单
"""

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import StringField, SelectField, FloatField, SubmitField
from wtforms.validators import DataRequired, Optional, NumberRange, ValidationError

from data.models import Category


class ProductSearchForm(FlaskForm):
    """
    产品搜索表单
    用于搜索产品
    """
    keyword = StringField('关键词', validators=[Optional()])
    category_id = SelectField('分类', coerce=int, validators=[Optional()])
    min_price = FloatField('最低价格', validators=[Optional(), NumberRange(min=0)])
    max_price = FloatField('最高价格', validators=[Optional(), NumberRange(min=0)])
    submit = SubmitField('搜索')
    
    def __init__(self, *args, **kwargs):
        super(ProductSearchForm, self).__init__(*args, **kwargs)
        # 动态加载分类选项
        self.category_id.choices = [(0, '所有分类')] + [
            (category.id, category.name) for category in Category.query.all()
        ]


class ProductFilterForm(FlaskForm):
    """
    产品过滤表单
    用于过滤产品列表
    """
    sort_by = SelectField('排序方式', choices=[
        ('created_at', '最新'),
        ('price', '价格'),
        ('sales', '销量'),
    ])
    order = SelectField('排序顺序', choices=[
        ('desc', '降序'),
        ('asc', '升序'),
    ])
    min_price = FloatField('最低价格', validators=[Optional(), NumberRange(min=0)])
    max_price = FloatField('最高价格', validators=[Optional(), NumberRange(min=0)])
    submit = SubmitField('应用')


class ProductImportForm(FlaskForm):
    """
    产品导入表单
    用于导入产品数据
    """
    file = FileField('JSON文件', validators=[DataRequired()])
    submit = SubmitField('导入')
    
    def validate_file(self, field):
        if not field.data.filename.endswith('.json'):
            raise ValidationError('只支持JSON文件')