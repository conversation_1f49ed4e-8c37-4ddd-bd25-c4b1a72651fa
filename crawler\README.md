# 电商数据爬虫模块

## 概述

本模块提供了用于爬取电商平台（主要是淘宝）电子产品数据的功能。它使用Selenium WebDriver模拟真实用户行为，能够有效地绕过反爬虫机制，获取产品的基本信息和详细信息。

## 主要组件

- **TaobaoCrawler**: 淘宝爬虫类，用于爬取淘宝电子产品数据
- **BaseCrawler**: 爬虫基类，提供浏览器初始化、页面访问等基础功能
- **UserBehavior**: 用户行为模拟类，用于模拟人类操作浏览器的行为
- **配置模块**: 提供爬虫配置信息，包括电子产品分类、关键词等
- **主程序**: 用于启动爬虫并保存数据

## 安装依赖

本模块依赖以下Python包：

```bash
pip install selenium webdriver-manager python-dotenv
```

此外，还需要安装Chrome浏览器，WebDriver会自动下载对应版本的ChromeDriver。

## 环境变量配置

在项目根目录创建`.env`文件，配置以下环境变量：

```
# 浏览器配置
HEADLESS=False  # 是否使用无头模式
TIMEOUT=30      # 页面加载超时时间（秒）
MAX_RETRIES=3   # 最大重试次数
DELAY_MIN=2     # 最小延迟时间（秒）
DELAY_MAX=5     # 最大延迟时间（秒）
```

## 使用方法

### 基本用法

```python
from crawler import TaobaoCrawler

# 创建爬虫实例
crawler = TaobaoCrawler()

try:
    # 爬取数据
    products = crawler.crawl(
        keywords=["智能手机", "iPhone"],
        pages_per_keyword=2,
        get_details=True
    )
    
    # 处理爬取的数据
    print(f"爬取到 {len(products)} 个产品数据")
    for product in products[:3]:  # 打印前3个产品信息
        print(f"产品: {product['title']}")
        print(f"价格: {product['price']}")
        print(f"销量: {product['sales']}")
        print("---")
        
finally:
    # 关闭爬虫
    crawler.close()
```

### 使用配置好的分类

```python
from crawler import TaobaoCrawler, ELECTRONIC_CATEGORIES

# 创建爬虫实例
crawler = TaobaoCrawler()

try:
    # 选择一个分类
    category = next(cat for cat in ELECTRONIC_CATEGORIES if cat["name"] == "手机")
    
    # 爬取该分类的数据
    products = crawler.crawl(
        keywords=category["keywords"],
        pages_per_keyword=1,
        get_details=False
    )
    
    print(f"爬取到 {len(products)} 个产品数据")
    
finally:
    # 关闭爬虫
    crawler.close()
```

### 使用主程序爬取所有分类

```python
from crawler import main

# 爬取所有分类
main(test_mode=True)  # 测试模式，只爬取每个分类的第一个关键词的第一页

# 爬取指定分类
main(categories=["手机", "笔记本电脑"], test_mode=False)
```

## 注意事项

1. **反爬虫风险**: 频繁爬取可能触发淘宝的反爬虫机制，建议合理设置延迟时间和爬取频率。

2. **登录问题**: 爬虫会尝试登录淘宝，需要手动扫码登录。如果不登录，可能会受到一些限制。

3. **IP限制**: 如果IP被淘宝限制，可能需要使用代理IP或等待一段时间后再尝试。

4. **数据存储**: 爬取的数据默认保存在`../data/raw`目录下的JSON文件中，可以根据需要修改保存路径和格式。

5. **浏览器驱动**: 如果遇到ChromeDriver相关问题，可能需要手动下载对应版本的ChromeDriver并配置路径。

## 扩展开发

如果需要爬取其他电商平台，可以参考`TaobaoCrawler`类的实现，继承`BaseCrawler`类并实现相应的方法。

```python
from crawler import BaseCrawler

class MyPlatformCrawler(BaseCrawler):
    def __init__(self):
        super().__init__()
        # 初始化代码
    
    def crawl(self, keywords, pages_per_keyword=1, get_details=False):
        # 实现爬取逻辑
        pass
```