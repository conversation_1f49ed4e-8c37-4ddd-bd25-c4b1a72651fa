# -*- coding: utf-8 -*-
"""
高级数据分析模块，实现更复杂的分析功能
"""

import pandas as pd
import numpy as np
import logging
from sqlalchemy import func, desc, and_, extract
from datetime import datetime, timedelta
import re

from data.db_connection import get_db_session, close_db_session
from data.models import Product, Category, Shop, PriceHistory, SalesHistory, RawData

# 配置日志
logger = logging.getLogger(__name__)

class AdvancedAnalyzer:
    """
    高级数据分析类，提供更复杂的分析功能
    """
    
    def __init__(self):
        """
        初始化分析器
        """
        self.session = get_db_session()
    
    def close(self):
        """
        关闭数据库会话
        """
        close_db_session()
    
    def get_time_series_data(self, category_name=None, days=30):
        """
        获取时间序列数据
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            days: 分析的天数
            
        Returns:
            DataFrame: 时间序列数据
        """
        try:
            # 计算开始日期
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 构建价格历史查询
            price_query = self.session.query(
                PriceHistory.date,
                func.avg(PriceHistory.price).label('avg_price')
            )
            
            # 构建销量历史查询
            sales_query = self.session.query(
                SalesHistory.date,
                func.sum(SalesHistory.sales).label('total_sales')
            )
            
            # 如果指定了分类，则筛选该分类的产品
            if category_name:
                price_query = price_query.join(Product, PriceHistory.product_id == Product.id)\
                    .join(Category, Product.category_id == Category.id)\
                    .filter(Category.name == category_name)
                
                sales_query = sales_query.join(Product, SalesHistory.product_id == Product.id)\
                    .join(Category, Product.category_id == Category.id)\
                    .filter(Category.name == category_name)
            
            # 筛选日期范围
            price_query = price_query.filter(and_(
                PriceHistory.date >= start_date,
                PriceHistory.date <= end_date
            ))
            
            sales_query = sales_query.filter(and_(
                SalesHistory.date >= start_date,
                SalesHistory.date <= end_date
            ))
            
            # 按日期分组
            price_query = price_query.group_by(PriceHistory.date)
            sales_query = sales_query.group_by(SalesHistory.date)
            
            # 执行查询
            price_df = pd.read_sql(price_query.statement, self.session.bind)
            sales_df = pd.read_sql(sales_query.statement, self.session.bind)
            
            # 合并数据
            if price_df.empty and sales_df.empty:
                logger.warning(f"未找到{'分类 ' + category_name if category_name else '任何'}的时间序列数据")
                return pd.DataFrame()
            
            # 确保日期列是日期类型
            if not price_df.empty:
                price_df['date'] = pd.to_datetime(price_df['date'])
            if not sales_df.empty:
                sales_df['date'] = pd.to_datetime(sales_df['date'])
            
            # 创建日期范围
            date_range = pd.date_range(start=start_date, end=end_date, freq='D')
            result_df = pd.DataFrame({'date': date_range})
            
            # 合并价格数据
            if not price_df.empty:
                result_df = pd.merge(result_df, price_df, on='date', how='left')
            else:
                result_df['avg_price'] = np.nan
            
            # 合并销量数据
            if not sales_df.empty:
                result_df = pd.merge(result_df, sales_df, on='date', how='left')
            else:
                result_df['total_sales'] = np.nan
            
            # 填充缺失值
            result_df['avg_price'] = result_df['avg_price'].fillna(method='ffill').fillna(method='bfill')
            result_df['total_sales'] = result_df['total_sales'].fillna(0)
            
            # 格式化数值
            result_df['avg_price'] = result_df['avg_price'].round(2)
            
            return result_df
        except Exception as e:
            logger.error(f"获取时间序列数据失败: {str(e)}")
            return pd.DataFrame()
    
    def get_brand_analysis(self, category_name=None):
        """
        获取品牌分析
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            
        Returns:
            DataFrame: 品牌分析
        """
        try:
            # 构建查询
            query = self.session.query(
                Product.title,
                Product.price,
                Product.sales,
                Product.rating
            )
            
            # 如果指定了分类，则筛选该分类的产品
            if category_name:
                query = query.join(Category, Product.category_id == Category.id)\
                    .filter(Category.name == category_name)
            
            # 执行查询
            products = pd.read_sql(query.statement, self.session.bind)
            
            if products.empty:
                logger.warning(f"未找到{'分类 ' + category_name if category_name else '任何'}的产品数据")
                return pd.DataFrame()
            
            # 提取品牌信息
            def extract_brand(title):
                # 常见电子产品品牌列表
                common_brands = [
                    '华为', '小米', '苹果', 'Apple', 'iPhone', 'iPad', 'HUAWEI', 'Xiaomi', 'Redmi', 
                    '三星', 'Samsung', 'OPPO', 'vivo', '荣耀', 'Honor', '联想', 'Lenovo', '戴尔', 'Dell',
                    '惠普', 'HP', '华硕', 'ASUS', '宏碁', 'Acer', '微软', 'Microsoft', 'Surface',
                    '索尼', 'Sony', '飞利浦', 'Philips', '松下', 'Panasonic', '佳能', 'Canon',
                    '尼康', 'Nikon', '罗技', 'Logitech', '雷蛇', 'Razer', '海信', 'Hisense',
                    'TCL', '创维', 'Skyworth', '长虹', 'Changhong', '海尔', 'Haier', '美的', 'Midea',
                    '格力', 'Gree', 'JBL', '博士', 'Bose', '索尼', 'Sony', '铁三角', 'Audio-Technica',
                    '森海塞尔', 'Sennheiser', '拜亚动力', 'Beyerdynamic', '漫步者', 'EDIFIER',
                    '魅族', 'Meizu', '一加', 'OnePlus', '诺基亚', 'Nokia', '摩托罗拉', 'Motorola',
                    '黑鲨', 'Black Shark', '努比亚', 'Nubia', '中兴', 'ZTE', '酷派', 'Coolpad',
                    '金立', 'Gionee', '360', '锤子', 'Smartisan', '真我', 'Realme'
                ]
                
                # 尝试匹配品牌
                for brand in common_brands:
                    if brand.lower() in title.lower():
                        return brand
                
                # 如果没有匹配到常见品牌，尝试提取第一个可能的品牌名
                match = re.search(r'^[\w\u4e00-\u9fa5]{1,8}\s', title)
                if match:
                    return match.group(0).strip()
                
                return '其他'
            
            # 应用品牌提取函数
            products['brand'] = products['title'].apply(extract_brand)
            
            # 按品牌分组
            brand_analysis = products.groupby('brand').agg({
                'title': 'count',
                'price': ['mean', 'min', 'max'],
                'sales': ['mean', 'sum'],
                'rating': 'mean'
            })
            
            # 重命名列
            brand_analysis.columns = [
                'product_count', 'avg_price', 'min_price', 'max_price',
                'avg_sales', 'total_sales', 'avg_rating'
            ]
            
            # 重置索引
            brand_analysis = brand_analysis.reset_index()
            
            # 格式化数值
            brand_analysis['avg_price'] = brand_analysis['avg_price'].round(2)
            brand_analysis['avg_sales'] = brand_analysis['avg_sales'].round(2)
            brand_analysis['avg_rating'] = brand_analysis['avg_rating'].round(2)
            
            # 计算市场份额
            total_sales = brand_analysis['total_sales'].sum()
            brand_analysis['market_share'] = (brand_analysis['total_sales'] / total_sales * 100).round(2)
            
            # 按产品数量排序
            brand_analysis = brand_analysis.sort_values('product_count', ascending=False)
            
            return brand_analysis
        except Exception as e:
            logger.error(f"获取品牌分析失败: {str(e)}")
            return pd.DataFrame()
    
    def get_price_trend_analysis(self, category_name=None, days=90):
        """
        获取价格趋势分析
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            days: 分析的天数
            
        Returns:
            dict: 价格趋势分析
        """
        try:
            # 获取时间序列数据
            time_series = self.get_time_series_data(category_name, days)
            
            if time_series.empty:
                return {
                    'trend': 'unknown',
                    'price_change': 0,
                    'price_change_percentage': 0,
                    'volatility': 0,
                    'min_price': 0,
                    'max_price': 0,
                    'time_series': []
                }
            
            # 计算价格变化
            first_price = time_series['avg_price'].iloc[0]
            last_price = time_series['avg_price'].iloc[-1]
            price_change = last_price - first_price
            price_change_percentage = (price_change / first_price * 100) if first_price > 0 else 0
            
            # 确定趋势
            if price_change_percentage > 5:
                trend = 'rising'
            elif price_change_percentage < -5:
                trend = 'falling'
            else:
                trend = 'stable'
            
            # 计算波动性（价格标准差与均值的比值）
            volatility = time_series['avg_price'].std() / time_series['avg_price'].mean() * 100
            
            # 获取最低和最高价格
            min_price = time_series['avg_price'].min()
            max_price = time_series['avg_price'].max()
            
            return {
                'trend': trend,
                'price_change': round(price_change, 2),
                'price_change_percentage': round(price_change_percentage, 2),
                'volatility': round(volatility, 2),
                'min_price': round(min_price, 2),
                'max_price': round(max_price, 2),
                'time_series': time_series.to_dict('records')
            }
        except Exception as e:
            logger.error(f"获取价格趋势分析失败: {str(e)}")
            return {
                'trend': 'unknown',
                'price_change': 0,
                'price_change_percentage': 0,
                'volatility': 0,
                'min_price': 0,
                'max_price': 0,
                'time_series': []
            }
    
    def get_seasonal_analysis(self, category_name=None):
        """
        获取季节性分析
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            
        Returns:
            dict: 季节性分析
        """
        try:
            # 构建销量历史查询
            query = self.session.query(
                extract('month', SalesHistory.date).label('month'),
                func.sum(SalesHistory.sales).label('total_sales')
            )
            
            # 如果指定了分类，则筛选该分类的产品
            if category_name:
                query = query.join(Product, SalesHistory.product_id == Product.id)\
                    .join(Category, Product.category_id == Category.id)\
                    .filter(Category.name == category_name)
            
            # 按月份分组
            query = query.group_by('month').order_by('month')
            
            # 执行查询
            monthly_sales = pd.read_sql(query.statement, self.session.bind)
            
            if monthly_sales.empty:
                logger.warning(f"未找到{'分类 ' + category_name if category_name else '任何'}的季节性数据")
                return {
                    'monthly_sales': [],
                    'quarterly_sales': [],
                    'peak_month': None,
                    'low_month': None
                }
            
            # 确保所有月份都有数据
            all_months = pd.DataFrame({'month': range(1, 13)})
            monthly_sales = pd.merge(all_months, monthly_sales, on='month', how='left')
            monthly_sales['total_sales'] = monthly_sales['total_sales'].fillna(0)
            
            # 添加月份名称
            month_names = ['一月', '二月', '三月', '四月', '五月', '六月', 
                          '七月', '八月', '九月', '十月', '十一月', '十二月']
            monthly_sales['month_name'] = monthly_sales['month'].apply(lambda x: month_names[int(x)-1])
            
            # 计算季度销量
            monthly_sales['quarter'] = ((monthly_sales['month'] - 1) // 3) + 1
            quarterly_sales = monthly_sales.groupby('quarter').agg({
                'total_sales': 'sum'
            }).reset_index()
            quarterly_sales['quarter_name'] = quarterly_sales['quarter'].apply(
                lambda x: f'Q{int(x)}'
            )
            
            # 找出销量最高和最低的月份
            peak_month_idx = monthly_sales['total_sales'].idxmax()
            low_month_idx = monthly_sales['total_sales'].idxmin()
            
            peak_month = {
                'month': int(monthly_sales.loc[peak_month_idx, 'month']),
                'month_name': monthly_sales.loc[peak_month_idx, 'month_name'],
                'sales': float(monthly_sales.loc[peak_month_idx, 'total_sales'])
            }
            
            low_month = {
                'month': int(monthly_sales.loc[low_month_idx, 'month']),
                'month_name': monthly_sales.loc[low_month_idx, 'month_name'],
                'sales': float(monthly_sales.loc[low_month_idx, 'total_sales'])
            }
            
            return {
                'monthly_sales': monthly_sales[['month', 'month_name', 'total_sales']].to_dict('records'),
                'quarterly_sales': quarterly_sales[['quarter', 'quarter_name', 'total_sales']].to_dict('records'),
                'peak_month': peak_month,
                'low_month': low_month
            }
        except Exception as e:
            logger.error(f"获取季节性分析失败: {str(e)}")
            return {
                'monthly_sales': [],
                'quarterly_sales': [],
                'peak_month': None,
                'low_month': None
            }
    
    def get_competitive_analysis(self, category_name):
        """
        获取竞争分析
        
        Args:
            category_name: 分类名称
            
        Returns:
            dict: 竞争分析
        """
        try:
            if not category_name:
                logger.error("竞争分析需要指定分类名称")
                return {
                    'brand_competition': [],
                    'price_segments': [],
                    'market_concentration': 0
                }
            
            # 获取品牌分析
            brand_analysis = self.get_brand_analysis(category_name)
            
            if brand_analysis.empty:
                return {
                    'brand_competition': [],
                    'price_segments': [],
                    'market_concentration': 0
                }
            
            # 计算市场集中度（前5大品牌的市场份额总和）
            top_brands = brand_analysis.head(5)
            market_concentration = top_brands['market_share'].sum()
            
            # 构建价格区间查询
            query = self.session.query(Product.price, Product.sales)\
                .join(Category, Product.category_id == Category.id)\
                .filter(Category.name == category_name)
            
            # 执行查询
            products = pd.read_sql(query.statement, self.session.bind)
            
            if products.empty:
                return {
                    'brand_competition': brand_analysis.to_dict('records'),
                    'price_segments': [],
                    'market_concentration': round(market_concentration, 2)
                }
            
            # 定义价格区间
            price_ranges = [
                (0, 500, '低端'),
                (500, 2000, '中低端'),
                (2000, 5000, '中端'),
                (5000, 10000, '中高端'),
                (10000, float('inf'), '高端')
            ]
            
            # 分析价格区间
            price_segments = []
            for start, end, segment in price_ranges:
                segment_products = products[(products['price'] >= start) & (products['price'] < end)]
                if not segment_products.empty:
                    product_count = len(segment_products)
                    total_sales = segment_products['sales'].sum()
                    avg_price = segment_products['price'].mean()
                    
                    price_segments.append({
                        'segment': segment,
                        'price_range': f"{start}-{end if end != float('inf') else '以上'}",
                        'product_count': product_count,
                        'total_sales': float(total_sales),
                        'avg_price': round(float(avg_price), 2),
                        'market_share': round(total_sales / products['sales'].sum() * 100, 2)
                    })
            
            return {
                'brand_competition': brand_analysis.to_dict('records'),
                'price_segments': price_segments,
                'market_concentration': round(market_concentration, 2)
            }
        except Exception as e:
            logger.error(f"获取竞争分析失败: {str(e)}")
            return {
                'brand_competition': [],
                'price_segments': [],
                'market_concentration': 0
            }

# 导出分析函数
def analyze_advanced_data(category_name=None):
    """
    高级分析数据的主函数
    
    Args:
        category_name: 分类名称，如果为None则分析所有产品
        
    Returns:
        dict: 分析结果
    """
    analyzer = AdvancedAnalyzer()
    try:
        # 执行各种高级分析
        time_series_data = analyzer.get_time_series_data(category_name)
        brand_analysis = analyzer.get_brand_analysis(category_name)
        price_trend_analysis = analyzer.get_price_trend_analysis(category_name)
        seasonal_analysis = analyzer.get_seasonal_analysis(category_name)
        
        # 如果指定了分类，则进行竞争分析
        competitive_analysis = {}
        if category_name:
            competitive_analysis = analyzer.get_competitive_analysis(category_name)
        
        # 返回分析结果
        return {
            'time_series_data': time_series_data.to_dict('records') if not time_series_data.empty else [],
            'brand_analysis': brand_analysis.to_dict('records') if not brand_analysis.empty else [],
            'price_trend_analysis': price_trend_analysis,
            'seasonal_analysis': seasonal_analysis,
            'competitive_analysis': competitive_analysis
        }
    finally:
        analyzer.close()

# 命令行入口
if __name__ == "__main__":
    import argparse
    import json
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="淘宝电子产品高级数据分析工具")
    parser.add_argument("--category", help="分类名称，如果不指定则分析所有产品")
    parser.add_argument("--output", help="输出文件路径，如果不指定则打印到控制台")
    
    args = parser.parse_args()
    
    # 分析数据
    result = analyze_advanced_data(args.category)
    
    # 输出结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"高级分析结果已保存到 {args.output}")
    else:
        print(json.dumps(result, ensure_ascii=False, indent=2))