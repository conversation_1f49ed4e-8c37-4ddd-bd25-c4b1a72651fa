# -*- coding: utf-8 -*-
"""
基础数据可视化模块，用于生成各种图表展示分析结果
"""

import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.font_manager import FontProperties
import matplotlib as mpl
from sqlalchemy import func
import json

from data.db_connection import get_db_session, close_db_session
from data.models import Product, Category, Shop, PriceHistory, SalesHistory, RawData
from analysis.basic_analysis import BasicAnalyzer

# 配置日志
logger = logging.getLogger(__name__)

# 配置中文字体
try:
    # 尝试设置中文字体
    if os.name == 'nt':  # Windows系统
        font_path = 'C:/Windows/Fonts/simhei.ttf'  # 黑体
        if os.path.exists(font_path):
            font = FontProperties(fname=font_path)
            plt.rcParams['font.family'] = font.get_name()
        else:
            logger.warning(f"找不到字体文件: {font_path}，将使用系统默认字体")
    else:  # Linux/Mac系统
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Heiti TC', 'Adobe Heiti Std', 'WenQuanYi Zen Hei']
    
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    
    # 设置Seaborn样式
    sns.set(style="whitegrid")
    sns.set_context("paper", font_scale=1.5)
    
    # 设置图表大小
    plt.rcParams["figure.figsize"] = (12, 8)
    
    # 设置DPI
    plt.rcParams["figure.dpi"] = 100
    
    # 设置颜色
    plt.rcParams["axes.prop_cycle"] = plt.cycler("color", plt.cm.tab10.colors)
    
    # 设置网格线
    plt.rcParams["grid.linestyle"] = "--"
    plt.rcParams["grid.alpha"] = 0.7
    
    # 设置刻度
    plt.rcParams["xtick.direction"] = "in"
    plt.rcParams["ytick.direction"] = "in"
    
    # 设置边框
    plt.rcParams["axes.spines.top"] = False
    plt.rcParams["axes.spines.right"] = False
    
    # 设置标题
    plt.rcParams["axes.titlesize"] = 18
    plt.rcParams["axes.titleweight"] = "bold"
    
    # 设置标签
    plt.rcParams["axes.labelsize"] = 14
    plt.rcParams["axes.labelweight"] = "bold"
    
    # 设置图例
    plt.rcParams["legend.frameon"] = True
    plt.rcParams["legend.framealpha"] = 0.8
    plt.rcParams["legend.fontsize"] = 12
    
    # 设置保存格式
    plt.rcParams["savefig.dpi"] = 300
    plt.rcParams["savefig.format"] = "png"
    plt.rcParams["savefig.bbox"] = "tight"
    plt.rcParams["savefig.transparent"] = True
    
    logger.info("Matplotlib和Seaborn配置成功")
    
except Exception as e:
    logger.error(f"配置Matplotlib和Seaborn失败: {e}")


class BasicCharts:
    """
    基础图表生成类，用于生成各种图表展示分析结果
    """
    
    def __init__(self, output_dir="../output/charts"):
        """
        初始化图表生成器
        
        Args:
            output_dir: 图表输出目录
        """
        self.output_dir = output_dir
        self.analyzer = BasicAnalyzer()
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
    
    def save_figure(self, fig, filename):
        """
        保存图表
        
        Args:
            fig: 图表对象
            filename: 文件名
        """
        filepath = os.path.join(self.output_dir, filename)
        try:
            fig.savefig(filepath, bbox_inches='tight')
            logger.info(f"图表已保存到: {filepath}")
        except Exception as e:
            logger.error(f"保存图表失败: {e}")
    
    def plot_category_distribution(self):
        """
        绘制分类分布图
        """
        try:
            # 获取分类统计数据
            category_stats = self.analyzer.get_category_stats()
            
            # 创建DataFrame
            df = pd.DataFrame(category_stats)
            
            # 按产品数量排序
            df = df.sort_values('product_count', ascending=False)
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(14, 8))
            
            # 绘制条形图
            bars = ax.bar(df['category_name'], df['product_count'], color=sns.color_palette("viridis", len(df)))
            
            # 添加数据标签
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 5,
                        f'{height:,}',
                        ha='center', va='bottom', fontsize=10)
            
            # 设置标题和标签
            ax.set_title('各分类产品数量分布')
            ax.set_xlabel('产品分类')
            ax.set_ylabel('产品数量')
            
            # 旋转x轴标签
            plt.xticks(rotation=45, ha='right')
            
            # 添加网格线
            ax.grid(axis='y', linestyle='--', alpha=0.7)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            self.save_figure(fig, 'category_distribution.png')
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制分类分布图失败: {e}")
            return None
    
    def plot_price_distribution(self, category_id=None):
        """
        绘制价格分布图
        
        Args:
            category_id: 分类ID，如果为None则绘制所有产品的价格分布
        """
        try:
            # 获取价格分布数据
            price_stats = self.analyzer.get_price_distribution(category_id)
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
            
            # 绘制直方图
            prices = [p['price'] for p in price_stats['products']]
            ax1.hist(prices, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.set_title('价格分布直方图')
            ax1.set_xlabel('价格 (元)')
            ax1.set_ylabel('产品数量')
            
            # 添加价格区间统计
            price_ranges = price_stats['price_ranges']
            ranges = [f"{r['min_price']}-{r['max_price']}" for r in price_ranges]
            counts = [r['count'] for r in price_ranges]
            
            # 绘制条形图
            bars = ax2.bar(ranges, counts, color=sns.color_palette("viridis", len(ranges)))
            
            # 添加数据标签
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height:,}',
                        ha='center', va='bottom', fontsize=10)
            
            ax2.set_title('价格区间分布')
            ax2.set_xlabel('价格区间 (元)')
            ax2.set_ylabel('产品数量')
            
            # 旋转x轴标签
            plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
            
            # 添加网格线
            ax1.grid(axis='y', linestyle='--', alpha=0.7)
            ax2.grid(axis='y', linestyle='--', alpha=0.7)
            
            # 添加统计信息
            stats_text = f"平均价格: {price_stats['avg_price']:.2f}元\n" \
                        f"中位数价格: {price_stats['median_price']:.2f}元\n" \
                        f"最高价格: {price_stats['max_price']:.2f}元\n" \
                        f"最低价格: {price_stats['min_price']:.2f}元\n" \
                        f"标准差: {price_stats['std_price']:.2f}"
            
            # 在图表上添加文本框
            props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
            ax1.text(0.05, 0.95, stats_text, transform=ax1.transAxes, fontsize=12,
                    verticalalignment='top', bbox=props)
            
            # 设置标题
            if category_id:
                session = get_db_session()
                try:
                    category = session.query(Category).filter(Category.id == category_id).first()
                    if category:
                        fig.suptitle(f"{category.name}类产品价格分布", fontsize=16)
                    else:
                        fig.suptitle("产品价格分布", fontsize=16)
                finally:
                    close_db_session(session)
            else:
                fig.suptitle("所有产品价格分布", fontsize=16)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            filename = f"price_distribution_{category_id}.png" if category_id else "price_distribution_all.png"
            self.save_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制价格分布图失败: {e}")
            return None
    
    def plot_sales_distribution(self, category_id=None):
        """
        绘制销量分布图
        
        Args:
            category_id: 分类ID，如果为None则绘制所有产品的销量分布
        """
        try:
            # 获取销量分布数据
            sales_stats = self.analyzer.get_sales_distribution(category_id)
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
            
            # 绘制直方图
            sales = [s['sales'] for s in sales_stats['products']]
            ax1.hist(sales, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
            ax1.set_title('销量分布直方图')
            ax1.set_xlabel('销量')
            ax1.set_ylabel('产品数量')
            
            # 添加销量区间统计
            sales_ranges = sales_stats['sales_ranges']
            ranges = [f"{r['min_sales']}-{r['max_sales']}" for r in sales_ranges]
            counts = [r['count'] for r in sales_ranges]
            
            # 绘制条形图
            bars = ax2.bar(ranges, counts, color=sns.color_palette("viridis", len(ranges)))
            
            # 添加数据标签
            for bar in bars:
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height:,}',
                        ha='center', va='bottom', fontsize=10)
            
            ax2.set_title('销量区间分布')
            ax2.set_xlabel('销量区间')
            ax2.set_ylabel('产品数量')
            
            # 旋转x轴标签
            plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
            
            # 添加网格线
            ax1.grid(axis='y', linestyle='--', alpha=0.7)
            ax2.grid(axis='y', linestyle='--', alpha=0.7)
            
            # 添加统计信息
            stats_text = f"平均销量: {sales_stats['avg_sales']:.2f}\n" \
                        f"中位数销量: {sales_stats['median_sales']:.2f}\n" \
                        f"最高销量: {sales_stats['max_sales']}\n" \
                        f"最低销量: {sales_stats['min_sales']}\n" \
                        f"标准差: {sales_stats['std_sales']:.2f}"
            
            # 在图表上添加文本框
            props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
            ax1.text(0.05, 0.95, stats_text, transform=ax1.transAxes, fontsize=12,
                    verticalalignment='top', bbox=props)
            
            # 设置标题
            if category_id:
                session = get_db_session()
                try:
                    category = session.query(Category).filter(Category.id == category_id).first()
                    if category:
                        fig.suptitle(f"{category.name}类产品销量分布", fontsize=16)
                    else:
                        fig.suptitle("产品销量分布", fontsize=16)
                finally:
                    close_db_session(session)
            else:
                fig.suptitle("所有产品销量分布", fontsize=16)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            filename = f"sales_distribution_{category_id}.png" if category_id else "sales_distribution_all.png"
            self.save_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制销量分布图失败: {e}")
            return None
    
    def plot_top_products(self, category_id=None, limit=20, by='sales'):
        """
        绘制热门产品排行图
        
        Args:
            category_id: 分类ID，如果为None则绘制所有产品
            limit: 显示的产品数量
            by: 排序依据，'sales'表示按销量排序，'price'表示按价格排序
        """
        try:
            # 获取热门产品数据
            top_products = self.analyzer.get_top_products(category_id, limit, by)
            
            # 创建DataFrame
            df = pd.DataFrame(top_products)
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(14, 10))
            
            # 绘制条形图
            if by == 'sales':
                bars = ax.barh(df['title'], df['sales'], color=sns.color_palette("viridis", len(df)))
                ax.set_xlabel('销量')
                title_suffix = '销量'
            else:  # by == 'price'
                bars = ax.barh(df['title'], df['price'], color=sns.color_palette("viridis", len(df)))
                ax.set_xlabel('价格 (元)')
                title_suffix = '价格'
            
            # 添加数据标签
            for bar in bars:
                width = bar.get_width()
                ax.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                        f'{width:,}',
                        ha='left', va='center', fontsize=10)
            
            # 设置标题和标签
            if category_id:
                session = get_db_session()
                try:
                    category = session.query(Category).filter(Category.id == category_id).first()
                    if category:
                        ax.set_title(f"{category.name}类热门产品{title_suffix}排行 (Top {limit})")
                    else:
                        ax.set_title(f"热门产品{title_suffix}排行 (Top {limit})")
                finally:
                    close_db_session(session)
            else:
                ax.set_title(f"热门产品{title_suffix}排行 (Top {limit})")
            
            ax.set_ylabel('产品名称')
            
            # 添加网格线
            ax.grid(axis='x', linestyle='--', alpha=0.7)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            filename = f"top_products_{by}_{category_id}.png" if category_id else f"top_products_{by}_all.png"
            self.save_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制热门产品排行图失败: {e}")
            return None
    
    def plot_top_shops(self, category_id=None, limit=20):
        """
        绘制热门店铺排行图
        
        Args:
            category_id: 分类ID，如果为None则绘制所有店铺
            limit: 显示的店铺数量
        """
        try:
            # 获取热门店铺数据
            top_shops = self.analyzer.get_top_shops(category_id, limit)
            
            # 创建DataFrame
            df = pd.DataFrame(top_shops)
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(14, 10))
            
            # 绘制条形图
            bars = ax.barh(df['shop_name'], df['product_count'], color=sns.color_palette("viridis", len(df)))
            
            # 添加数据标签
            for bar in bars:
                width = bar.get_width()
                ax.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                        f'{width:,}',
                        ha='left', va='center', fontsize=10)
            
            # 设置标题和标签
            if category_id:
                session = get_db_session()
                try:
                    category = session.query(Category).filter(Category.id == category_id).first()
                    if category:
                        ax.set_title(f"{category.name}类热门店铺排行 (Top {limit})")
                    else:
                        ax.set_title(f"热门店铺排行 (Top {limit})")
                finally:
                    close_db_session(session)
            else:
                ax.set_title(f"热门店铺排行 (Top {limit})")
            
            ax.set_xlabel('产品数量')
            ax.set_ylabel('店铺名称')
            
            # 添加网格线
            ax.grid(axis='x', linestyle='--', alpha=0.7)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            filename = f"top_shops_{category_id}.png" if category_id else "top_shops_all.png"
            self.save_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制热门店铺排行图失败: {e}")
            return None
    
    def plot_location_distribution(self, category_id=None, limit=20):
        """
        绘制地区分布图
        
        Args:
            category_id: 分类ID，如果为None则绘制所有产品
            limit: 显示的地区数量
        """
        try:
            # 获取地区分布数据
            location_stats = self.analyzer.get_location_distribution(category_id, limit)
            
            # 创建DataFrame
            df = pd.DataFrame(location_stats)
            
            # 创建图表
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 10))
            
            # 绘制条形图
            bars = ax1.bar(df['location'], df['count'], color=sns.color_palette("viridis", len(df)))
            
            # 添加数据标签
            for bar in bars:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{height:,}',
                        ha='center', va='bottom', fontsize=10)
            
            ax1.set_title('地区分布条形图')
            ax1.set_xlabel('地区')
            ax1.set_ylabel('产品数量')
            
            # 旋转x轴标签
            plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
            
            # 绘制饼图
            ax2.pie(df['count'], labels=df['location'], autopct='%1.1f%%',
                   shadow=True, startangle=90, colors=sns.color_palette("viridis", len(df)))
            ax2.axis('equal')  # 确保饼图是圆的
            ax2.set_title('地区分布饼图')
            
            # 设置标题
            if category_id:
                session = get_db_session()
                try:
                    category = session.query(Category).filter(Category.id == category_id).first()
                    if category:
                        fig.suptitle(f"{category.name}类产品地区分布 (Top {limit})", fontsize=16)
                    else:
                        fig.suptitle(f"产品地区分布 (Top {limit})", fontsize=16)
                finally:
                    close_db_session(session)
            else:
                fig.suptitle(f"产品地区分布 (Top {limit})", fontsize=16)
            
            # 添加网格线
            ax1.grid(axis='y', linestyle='--', alpha=0.7)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            filename = f"location_distribution_{category_id}.png" if category_id else "location_distribution_all.png"
            self.save_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制地区分布图失败: {e}")
            return None
    
    def plot_keyword_distribution(self, category_id=None, limit=20):
        """
        绘制关键词分布图
        
        Args:
            category_id: 分类ID，如果为None则绘制所有产品
            limit: 显示的关键词数量
        """
        try:
            # 获取关键词分布数据
            keyword_stats = self.analyzer.get_keyword_stats(category_id, limit)
            
            # 创建DataFrame
            df = pd.DataFrame(keyword_stats)
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(14, 10))
            
            # 绘制条形图
            bars = ax.barh(df['keyword'], df['count'], color=sns.color_palette("viridis", len(df)))
            
            # 添加数据标签
            for bar in bars:
                width = bar.get_width()
                ax.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                        f'{width:,}',
                        ha='left', va='center', fontsize=10)
            
            # 设置标题和标签
            if category_id:
                session = get_db_session()
                try:
                    category = session.query(Category).filter(Category.id == category_id).first()
                    if category:
                        ax.set_title(f"{category.name}类产品关键词分布 (Top {limit})")
                    else:
                        ax.set_title(f"产品关键词分布 (Top {limit})")
                finally:
                    close_db_session(session)
            else:
                ax.set_title(f"产品关键词分布 (Top {limit})")
            
            ax.set_xlabel('出现次数')
            ax.set_ylabel('关键词')
            
            # 添加网格线
            ax.grid(axis='x', linestyle='--', alpha=0.7)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            filename = f"keyword_distribution_{category_id}.png" if category_id else "keyword_distribution_all.png"
            self.save_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制关键词分布图失败: {e}")
            return None
    
    def plot_price_sales_correlation(self, category_id=None):
        """
        绘制价格与销量的相关性图
        
        Args:
            category_id: 分类ID，如果为None则绘制所有产品
        """
        try:
            # 获取价格与销量的相关性数据
            correlation_data = self.analyzer.get_price_sales_correlation(category_id)
            
            # 创建DataFrame
            df = pd.DataFrame(correlation_data['products'])
            
            # 创建图表
            fig, ax = plt.subplots(figsize=(12, 10))
            
            # 绘制散点图
            scatter = ax.scatter(df['price'], df['sales'], alpha=0.6, c=df['price'], 
                               cmap='viridis', s=50, edgecolors='w', linewidths=0.5)
            
            # 添加趋势线
            z = np.polyfit(df['price'], df['sales'], 1)
            p = np.poly1d(z)
            ax.plot(df['price'], p(df['price']), "r--", linewidth=2)
            
            # 设置标题和标签
            if category_id:
                session = get_db_session()
                try:
                    category = session.query(Category).filter(Category.id == category_id).first()
                    if category:
                        ax.set_title(f"{category.name}类产品价格与销量的相关性")
                    else:
                        ax.set_title("产品价格与销量的相关性")
                finally:
                    close_db_session(session)
            else:
                ax.set_title("产品价格与销量的相关性")
            
            ax.set_xlabel('价格 (元)')
            ax.set_ylabel('销量')
            
            # 添加相关系数
            correlation = correlation_data['correlation']
            correlation_text = f"相关系数: {correlation:.4f}"
            
            # 在图表上添加文本框
            props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
            ax.text(0.05, 0.95, correlation_text, transform=ax.transAxes, fontsize=12,
                   verticalalignment='top', bbox=props)
            
            # 添加颜色条
            cbar = plt.colorbar(scatter)
            cbar.set_label('价格 (元)')
            
            # 添加网格线
            ax.grid(linestyle='--', alpha=0.7)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图表
            filename = f"price_sales_correlation_{category_id}.png" if category_id else "price_sales_correlation_all.png"
            self.save_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制价格与销量的相关性图失败: {e}")
            return None
    
    def generate_all_charts(self, category_id=None):
        """
        生成所有图表
        
        Args:
            category_id: 分类ID，如果为None则生成所有产品的图表
        """
        # 生成分类分布图
        if category_id is None:
            self.plot_category_distribution()
        
        # 生成价格分布图
        self.plot_price_distribution(category_id)
        
        # 生成销量分布图
        self.plot_sales_distribution(category_id)
        
        # 生成热门产品排行图（按销量）
        self.plot_top_products(category_id, by='sales')
        
        # 生成热门产品排行图（按价格）
        self.plot_top_products(category_id, by='price')
        
        # 生成热门店铺排行图
        self.plot_top_shops(category_id)
        
        # 生成地区分布图
        self.plot_location_distribution(category_id)
        
        # 生成关键词分布图
        self.plot_keyword_distribution(category_id)
        
        # 生成价格与销量的相关性图
        self.plot_price_sales_correlation(category_id)
        
        logger.info("所有图表生成完成")


def main():
    """
    主函数
    """
    import argparse
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='数据可视化工具')
    parser.add_argument('--category_id', type=int, help='分类ID')
    parser.add_argument('--output_dir', type=str, default='../output/charts', help='图表输出目录')
    parser.add_argument('--chart_type', type=str, choices=[
        'category', 'price', 'sales', 'top_products', 'top_shops',
        'location', 'keyword', 'correlation', 'all'
    ], default='all', help='图表类型')
    
    args = parser.parse_args()
    
    # 创建图表生成器
    charts = BasicCharts(output_dir=args.output_dir)
    
    # 根据参数生成相应的图表
    if args.chart_type == 'category' and args.category_id is None:
        charts.plot_category_distribution()
    elif args.chart_type == 'price':
        charts.plot_price_distribution(args.category_id)
    elif args.chart_type == 'sales':
        charts.plot_sales_distribution(args.category_id)
    elif args.chart_type == 'top_products':
        charts.plot_top_products(args.category_id, by='sales')
        charts.plot_top_products(args.category_id, by='price')
    elif args.chart_type == 'top_shops':
        charts.plot_top_shops(args.category_id)
    elif args.chart_type == 'location':
        charts.plot_location_distribution(args.category_id)
    elif args.chart_type == 'keyword':
        charts.plot_keyword_distribution(args.category_id)
    elif args.chart_type == 'correlation':
        charts.plot_price_sales_correlation(args.category_id)
    else:  # args.chart_type == 'all'
        charts.generate_all_charts(args.category_id)
    
    logger.info("图表生成完成")


if __name__ == "__main__":
    main()