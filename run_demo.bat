@echo off
echo ===================================================
echo 电商数据生成、分析与可视化演示
echo ===================================================
echo.

echo 步骤1: 重新初始化数据库并导入模拟数据
echo ---------------------------------------------------
python -m examples.reinitialize_and_import --all --count 30
echo.

echo 步骤2: 运行数据分析
echo ---------------------------------------------------
python -m examples.data_analysis_example
echo.

echo 步骤3: 启动Web仪表盘
echo ---------------------------------------------------
echo Web仪表盘将在 http://127.0.0.1:5000 上运行
echo 按Ctrl+C停止服务
echo.
python run_dashboard.py

pause