{% extends "layouts/base.html" %}

{% block title %}首页 - 电商数据分析系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 欢迎信息 -->
    <div class="col-12 mb-4">
        <div class="card bg-primary text-white">
            <div class="card-body p-5 text-center">
                <h1 class="display-4">欢迎使用电商数据分析系统</h1>
                <p class="lead">一个强大的电商数据分析和可视化平台，帮助您洞察市场趋势和产品表现</p>
                <a href="{{ url_for('main.dashboard') }}" class="btn btn-light btn-lg mt-3">
                    <i class="fas fa-tachometer-alt me-2"></i>查看仪表盘
                </a>
            </div>
        </div>
    </div>

    <!-- 系统概览 -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-light">
                <h4 class="mb-0"><i class="fas fa-info-circle me-2"></i>系统概览</h4>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="card bg-primary text-white h-100">
                            <div class="card-body">
                                <h1 class="display-4">{{ stats.product_count }}</h1>
                                <p class="lead">产品总数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-success text-white h-100">
                            <div class="card-body">
                                <h1 class="display-4">{{ stats.category_count }}</h1>
                                <p class="lead">分类总数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-info text-white h-100">
                            <div class="card-body">
                                <h1 class="display-4">{{ stats.shop_count }}</h1>
                                <p class="lead">店铺总数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card bg-warning text-dark h-100">
                            <div class="card-body">
                                <h1 class="display-4">¥{{ stats.avg_price|round(2) }}</h1>
                                <p class="lead">平均价格</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能模块 -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-light">
                <h4 class="mb-0"><i class="fas fa-th-large me-2"></i>功能模块</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-shopping-cart fa-4x text-primary mb-3"></i>
                                <h4>产品管理</h4>
                                <p>浏览、搜索和分析电商产品数据</p>
                                <a href="{{ url_for('products.product_list') }}" class="btn btn-primary">查看产品</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-bar fa-4x text-success mb-3"></i>
                                <h4>数据分析</h4>
                                <p>深入分析产品数据，发现市场趋势</p>
                                <a href="{{ url_for('analysis.basic_analysis') }}" class="btn btn-success">开始分析</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="fas fa-chart-pie fa-4x text-info mb-3"></i>
                                <h4>数据可视化</h4>
                                <p>通过图表直观展示数据分析结果</p>
                                <a href="{{ url_for('visualization.basic_charts') }}" class="btn btn-info text-white">查看图表</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最近添加的产品 -->
    <div class="col-md-8 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h4 class="mb-0"><i class="fas fa-clock me-2"></i>最近添加的产品</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>产品名称</th>
                                <th>分类</th>
                                <th>价格</th>
                                <th>销量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in recent_products %}
                            <tr>
                                <td>{{ product.name }}</td>
                                <td>{{ product.category }}</td>
                                <td>¥{{ product.price }}</td>
                                <td>{{ product.sales_count }}</td>
                                <td>
                                    <a href="{{ url_for('products.product_detail', product_id=product.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer text-end">
                <a href="{{ url_for('products.product_list') }}" class="btn btn-outline-primary">查看全部</a>
            </div>
        </div>
    </div>

    <!-- 热门分类 -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h4 class="mb-0"><i class="fas fa-tags me-2"></i>热门分类</h4>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for category in top_categories %}
                    <a href="{{ url_for('products.products_by_category', category=category.name) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        {{ category.name }}
                        <span class="badge bg-primary rounded-pill">{{ category.count }}</span>
                    </a>
                    {% endfor %}
                </div>
            </div>
            <div class="card-footer text-end">
                <a href="{{ url_for('analysis.basic_analysis') }}" class="btn btn-outline-primary">查看分析</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}