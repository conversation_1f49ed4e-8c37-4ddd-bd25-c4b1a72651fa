# -*- coding: utf-8 -*-
"""
数据生成器脚本

该脚本用于生成模拟的电商产品数据，替代爬虫爬取的数据
"""

import os
import sys
import json
import random
import logging
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入爬虫配置
from crawler.config import ELECTRONIC_CATEGORIES

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("data_generator.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 品牌配置
BRANDS = {
    "手机": [
        "Apple/苹果", "HUAWEI/华为", "Xiaomi/小米", "OPPO", "vivo", 
        "Samsung/三星", "Honor/荣耀", "OnePlus/一加", "realme", "魅族"
    ],
    "笔记本电脑": [
        "Apple/苹果", "HUAWEI/华为", "Xiaomi/小米", "Lenovo/联想", "Dell/戴尔",
        "HP/惠普", "ASUS/华硕", "Acer/宏碁", "Microsoft/微软", "Razer/雷蛇"
    ],
    "平板电脑": [
        "Apple/苹果", "HUAWEI/华为", "Xiaomi/小米", "Samsung/三星", "Lenovo/联想",
        "Microsoft/微软", "Honor/荣耀", "OPPO", "vivo", "Teclast/台电"
    ],
    "智能手表": [
        "Apple/苹果", "HUAWEI/华为", "Xiaomi/小米", "OPPO", "Samsung/三星",
        "Honor/荣耀", "Garmin/佳明", "Amazfit/华米", "Fitbit", "SUUNTO/颂拓"
    ],
    "耳机": [
        "Apple/苹果", "HUAWEI/华为", "Xiaomi/小米", "OPPO", "vivo",
        "Sony/索尼", "Bose", "Sennheiser/森海塞尔", "JBL", "Beats"
    ],
    "智能音箱": [
        "Xiaomi/小米", "HUAWEI/华为", "Alibaba/阿里巴巴", "Baidu/百度", "JBL",
        "Harman Kardon/哈曼卡顿", "Sony/索尼", "Bose", "Marshall", "SONOS"
    ],
    "相机": [
        "Canon/佳能", "Nikon/尼康", "Sony/索尼", "Fujifilm/富士", "Panasonic/松下",
        "Olympus/奥林巴斯", "Leica/徕卡", "GoPro", "DJI/大疆", "Insta360"
    ],
    "电视": [
        "Xiaomi/小米", "HUAWEI/华为", "Hisense/海信", "Sony/索尼", "Samsung/三星",
        "TCL", "Skyworth/创维", "Changhong/长虹", "Konka/康佳", "LG"
    ],
    "游戏设备": [
        "Sony/索尼", "Microsoft/微软", "Nintendo/任天堂", "Logitech/罗技", "Razer/雷蛇",
        "SteelSeries/赛睿", "HyperX", "Corsair/海盗船", "ASUS/华硕", "MSI/微星"
    ],
    "智能家居": [
        "Xiaomi/小米", "HUAWEI/华为", "Aqara/绿米", "Philips/飞利浦", "TP-Link",
        "Midea/美的", "Haier/海尔", "Gree/格力", "Roborock/石头", "Ecovacs/科沃斯"
    ],
    "电脑配件": [
        "Logitech/罗技", "Razer/雷蛇", "Kingston/金士顿", "Samsung/三星", "Western Digital/西部数据",
        "Seagate/希捷", "NVIDIA/英伟达", "AMD", "Intel/英特尔", "ASUS/华硕"
    ]
}

# 城市列表
CITIES = ["北京", "上海", "广州", "深圳", "杭州", "成都", "武汉", "西安", "南京", "重庆",
         "苏州", "天津", "长沙", "郑州", "东莞", "青岛", "沈阳", "宁波", "昆明"]

# 店铺名称模板
SHOP_NAME_TEMPLATES = [
    "{brand}官方旗舰店",
    "{brand}专卖店",
    "{brand}{city}专卖店",
    "{brand}授权店",
    "{city}{brand}专营店",
    "{brand}直营店",
    "{brand}官方自营店",
    "{brand}京东自营",
    "{brand}天猫旗舰店",
    "{brand}官方直营"
]

# 产品标题模板
TITLE_TEMPLATES = {
    "手机": [
        "【{discount}】{brand} {model} {storage}GB {color}色 {network}手机官方旗舰店正品",
        "{brand} {model} {storage}GB {ram}GB运存 {color}色 {network}手机",
        "新款{brand} {model} {storage}GB {network}手机 {color}色 官方正品",
        "{brand} {model} 全网通 {storage}GB {color} 官方旗舰店正品手机",
        "【现货速发】{brand} {model} {storage}GB {color}色 {network}手机"
    ],
    "笔记本电脑": [
        "{brand} {model} {cpu} {ram}GB {storage}GB {screen}英寸 {color}色 笔记本电脑",
        "【{discount}】{brand} {model} {cpu} {ram}GB内存 {storage}GB固态 轻薄本",
        "新款{brand} {model} {cpu} {ram}GB {storage}GB {screen}英寸 笔记本电脑",
        "{brand} {model} {cpu} {ram}GB {storage}GB {screen}英寸 {type}本 {color}",
        "【现货速发】{brand} {model} {cpu} {ram}GB {storage}GB {screen}英寸笔记本"
    ],
    "平板电脑": [
        "{brand} {model} {storage}GB {screen}英寸 {color}色 {network}平板电脑",
        "【{discount}】{brand} {model} {storage}GB {screen}英寸 {color} 平板电脑",
        "新款{brand} {model} {storage}GB {screen}英寸 {color}色 {network}平板",
        "{brand} {model} {storage}GB {screen}英寸 {color} 官方旗舰店平板电脑",
        "【现货速发】{brand} {model} {storage}GB {screen}英寸 {color}色平板"
    ],
    "智能手表": [
        "{brand} {model} {size}mm {color}色 {material}表壳 智能手表",
        "【{discount}】{brand} {model} {size}mm {color} {material}表壳 智能手表",
        "新款{brand} {model} {size}mm {color}色 {material}表壳 智能手表",
        "{brand} {model} {size}mm {color} {material}表壳 官方旗舰店智能手表",
        "【现货速发】{brand} {model} {size}mm {color}色 {material}表壳 智能手表"
    ],
    "耳机": [
        "{brand} {model} {color}色 {type}耳机 {feature}降噪",
        "【{discount}】{brand} {model} {color} {type}耳机 {feature}",
        "新款{brand} {model} {color}色 {type}耳机 {feature}",
        "{brand} {model} {color} {type}耳机 官方旗舰店正品",
        "【现货速发】{brand} {model} {color}色 {type}耳机 {feature}"
    ]
}

# 其他分类使用通用模板
for category in BRANDS.keys():
    if category not in TITLE_TEMPLATES:
        TITLE_TEMPLATES[category] = [
            "{brand} {model} {color}色 {feature} {category}",
            "【{discount}】{brand} {model} {color} {feature} {category}",
            "新款{brand} {model} {color}色 {feature} {category}",
            "{brand} {model} {color} {feature} 官方旗舰店正品{category}",
            "【现货速发】{brand} {model} {color}色 {feature} {category}"
        ]

# 产品属性配置
PRODUCT_ATTRIBUTES = {
    "手机": {
        "model": ["iPhone 14 Pro Max", "iPhone 14 Pro", "iPhone 14", "iPhone 13", 
                 "Mate 60 Pro", "Mate 60", "P60 Pro", "P60", 
                 "小米14 Ultra", "小米14 Pro", "小米14", "小米13", 
                 "Find X6 Pro", "Find X6", "Find X5", 
                 "X100 Pro", "X100", "X90 Pro", "X90",
                 "Galaxy S23 Ultra", "Galaxy S23+", "Galaxy S23", 
                 "Magic6 Pro", "Magic6", "Magic5", 
                 "OnePlus 12", "OnePlus 11", 
                 "GT Neo5", "GT Neo5 SE"],
        "storage": ["64", "128", "256", "512", "1024"],
        "ram": ["4", "6", "8", "12", "16"],
        "color": ["黑色", "白色", "蓝色", "绿色", "紫色", "金色", "银色", "红色", "粉色", "灰色"],
        "network": ["5G", "全网通", "双模5G"],
        "screen": ["6.1", "6.3", "6.5", "6.7", "6.8", "6.9", "7.0"]
    },
    "笔记本电脑": {
        "model": ["MacBook Pro", "MacBook Air", "MateBook X Pro", "MateBook 14", 
                 "RedmiBook Pro", "小米笔记本Pro", "ThinkPad X1 Carbon", "ThinkPad E14", 
                 "XPS 13", "Inspiron 14", "惠普星", "惠普暗影精灵", 
                 "华硕灵耀", "华硕天选", "宏碁暗影骑士", "宏碁蜂鸟", 
                 "Surface Laptop", "Surface Book", "雷蛇灵刃", "雷蛇战刃"],
        "cpu": ["Intel i9", "Intel i7", "Intel i5", "Intel i3", "AMD R9", "AMD R7", "AMD R5", "M2 Pro", "M2 Max", "M2", "M1"],
        "ram": ["8", "16", "32", "64"],
        "storage": ["256", "512", "1024", "2048"],
        "screen": ["13.3", "14", "15.6", "16", "17.3"],
        "color": ["深空灰", "银色", "黑色", "白色", "蓝色", "粉色"],
        "type": ["轻薄", "游戏", "商务", "设计师", "学生"]
    }
}

# 为其他分类添加通用属性
for category in BRANDS.keys():
    if category not in PRODUCT_ATTRIBUTES:
        PRODUCT_ATTRIBUTES[category] = {
            "model": [f"{brand.split('/')[-1]}型号{i}" for i in range(1, 6) for brand in BRANDS[category]],
            "color": ["黑色", "白色", "蓝色", "绿色", "紫色", "金色", "银色", "红色", "粉色", "灰色"],
            "feature": ["高端", "入门级", "专业级", "旗舰", "性价比", "轻薄", "便携", "高性能", "智能", "防水"]
        }

# 折扣信息
DISCOUNTS = ["12期免息", "24期免息", "限时优惠", "赠送好礼", "限时特惠", "新品上市", "现货速发", "官方授权", "顺丰包邮", "赠运费险"]

# 店铺URL模板
SHOP_URL_TEMPLATES = [
    "https://{brand_lower}.tmall.com",
    "https://mall.jd.com/{brand_lower}",
    "https://shop{shop_id}.taobao.com",
    "https://{brand_lower}.jd.com",
    "https://store.tmall.com/{brand_lower}"
]

class DataGenerator:
    """
    数据生成器类，用于生成模拟的电商产品数据
    """
    
    def __init__(self):
        """
        初始化数据生成器
        """
        self.product_id_counter = 10000000000
    
    def generate_product_id(self):
        """
        生成唯一的产品ID
        
        Returns:
            str: 产品ID
        """
        self.product_id_counter += 1
        return str(self.product_id_counter)
    
    def generate_shop_data(self, brand, category):
        """
        生成店铺数据
        
        Args:
            brand: 品牌名称
            category: 产品分类
            
        Returns:
            dict: 店铺数据
        """
        brand_simple = brand.split('/')[0]
        brand_lower = brand_simple.lower()
        city = random.choice(CITIES)
        shop_name = random.choice(SHOP_NAME_TEMPLATES).format(brand=brand_simple, city=city)
        shop_id = random.randint(100000, 999999)
        shop_url = random.choice(SHOP_URL_TEMPLATES).format(brand_lower=brand_lower, shop_id=shop_id)
        
        return {
            "shop_name": shop_name,
            "shop_url": shop_url
        }
    
    def generate_product_attributes(self, category, brand):
        """
        生成产品属性
        
        Args:
            category: 产品分类
            brand: 品牌名称
            
        Returns:
            dict: 产品属性
        """
        attributes = {}
        category_attrs = PRODUCT_ATTRIBUTES.get(category, {})
        
        # 添加品牌
        attributes["品牌"] = brand
        
        # 添加其他属性
        for attr_name, attr_values in category_attrs.items():
            if attr_values:
                attributes[attr_name] = random.choice(attr_values)
        
        return attributes
    
    def generate_product_title(self, category, attributes):
        """
        生成产品标题
        
        Args:
            category: 产品分类
            attributes: 产品属性
            
        Returns:
            str: 产品标题
        """
        templates = TITLE_TEMPLATES.get(category, TITLE_TEMPLATES["手机"])
        template = random.choice(templates)
        
        # 准备格式化参数
        format_params = {
            "brand": attributes.get("品牌", "").split('/')[0],
            "model": attributes.get("model", ""),
            "color": attributes.get("color", ""),
            "category": category,
            "discount": random.choice(DISCOUNTS),
            "feature": attributes.get("feature", "")
        }
        
        # 添加分类特定的参数
        if category == "手机":
            format_params.update({
                "storage": attributes.get("storage", ""),
                "ram": attributes.get("ram", ""),
                "network": attributes.get("network", "")
            })
        elif category == "笔记本电脑":
            format_params.update({
                "cpu": attributes.get("cpu", ""),
                "ram": attributes.get("ram", ""),
                "storage": attributes.get("storage", ""),
                "screen": attributes.get("screen", ""),
                "type": attributes.get("type", "")
            })
        elif category == "平板电脑":
            format_params.update({
                "storage": attributes.get("storage", ""),
                "screen": attributes.get("screen", ""),
                "network": attributes.get("network", "")
            })
        elif category == "智能手表":
            format_params.update({
                "size": attributes.get("size", ""),
                "material": attributes.get("material", "")
            })
        elif category == "耳机":
            format_params.update({
                "type": attributes.get("type", ""),
                "feature": attributes.get("feature", "")
            })
        
        # 尝试格式化标题，如果缺少参数则跳过
        try:
            title = template.format(**format_params)
            return title
        except KeyError:
            # 如果格式化失败，使用简单的标题格式
            return f"{format_params['brand']} {format_params['model']} {format_params['color']} {category}"
    
    def generate_product_data(self, category_name, keyword):
        """
        生成单个产品数据
        
        Args:
            category_name: 分类名称
            keyword: 搜索关键词
            
        Returns:
            dict: 产品数据
        """
        # 生成产品ID
        product_id = self.generate_product_id()
        
        # 选择品牌
        brand = random.choice(BRANDS.get(category_name, BRANDS["手机"]))
        
        # 生成产品属性
        attributes = self.generate_product_attributes(category_name, brand)
        
        # 生成产品标题
        title = self.generate_product_title(category_name, attributes)
        
        # 生成店铺信息
        shop_data = self.generate_shop_data(brand, category_name)
        
        # 生成价格和销量
        price = round(random.uniform(100, 10000), 2)
        sales = random.randint(0, 10000)
        
        # 生成评分和评论数
        rating = round(random.uniform(4.0, 5.0), 1)
        review_count = random.randint(0, 20000)
        
        # 生成位置信息
        location = random.choice(CITIES)
        
        # 生成爬取时间
        now = datetime.now()
        crawl_time = (now - timedelta(days=random.randint(0, 30), 
                                    hours=random.randint(0, 23), 
                                    minutes=random.randint(0, 59))).strftime("%Y-%m-%d %H:%M:%S")
        
        # 生成详情页URL
        detail_url = f"https://detail.tmall.com/item.htm?id={product_id}"
        
        # 构建产品数据
        product_data = {
            "product_id": product_id,
            "title": title,
            "price": f"{price:.2f}",
            "sales": str(sales),
            "detail_url": detail_url,
            "shop_name": shop_data["shop_name"],
            "shop_url": shop_data["shop_url"],
            "location": location,
            "crawl_time": crawl_time,
            "keyword": keyword,
            "attributes": attributes,
            "rating": str(rating),
            "review_count": str(review_count)
        }
        
        return product_data
    
    def generate_category_data(self, category, num_products=50):
        """
        生成指定分类的产品数据
        
        Args:
            category: 分类配置
            num_products: 产品数量
            
        Returns:
            list: 产品数据列表
        """
        products = []
        category_name = category["name"]
        keywords = category["keywords"]
        
        logger.info(f"开始生成分类 {category_name} 的数据")
        
        # 为每个关键词生成产品数据
        for keyword in keywords:
            # 每个关键词生成的产品数量
            products_per_keyword = num_products // len(keywords)
            
            logger.info(f"为关键词 {keyword} 生成 {products_per_keyword} 条产品数据")
            
            for _ in range(products_per_keyword):
                product = self.generate_product_data(category_name, keyword)
                products.append(product)
        
        logger.info(f"分类 {category_name} 共生成 {len(products)} 条产品数据")
        return products
    
    def save_to_json(self, products, category_name):
        """
        将产品数据保存为JSON文件
        
        Args:
            products: 产品数据列表
            category_name: 分类名称
            
        Returns:
            str: 文件路径
        """
        # 创建输出目录
        output_dir = os.path.join(os.path.dirname(__file__), "../data/raw")
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"{category_name}_{timestamp}.json")
        
        # 保存数据
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(products, f, ensure_ascii=False, indent=2)
        
        logger.info(f"成功将 {len(products)} 条产品数据保存到 {filename}")
        return filename

def generate_and_save(category_name, num_products=50):
    """
    生成并保存指定分类的数据
    
    Args:
        category_name: 分类名称
        num_products: 产品数量
        
    Returns:
        str: 文件路径
    """
    # 查找对应的分类配置
    category = None
    for cat in ELECTRONIC_CATEGORIES:
        if cat["name"] == category_name:
            category = cat
            break
    
    if not category:
        logger.error(f"未找到分类: {category_name}")
        return None
    
    # 创建数据生成器
    generator = DataGenerator()
    
    # 生成数据
    products = generator.generate_category_data(category, num_products)
    
    # 保存数据
    return generator.save_to_json(products, category_name)

def generate_all_categories(num_products_per_category=50):
    """
    生成所有分类的数据
    
    Args:
        num_products_per_category: 每个分类的产品数量
        
    Returns:
        list: 文件路径列表
    """
    files = []
    
    for category in ELECTRONIC_CATEGORIES:
        file_path = generate_and_save(category["name"], num_products_per_category)
        if file_path:
            files.append(file_path)
    
    return files

def main():
    """
    主函数
    """
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="电商产品数据生成工具")
    parser.add_argument("--category", default="手机", help="要生成的分类名称")
    parser.add_argument("--num", type=int, default=50, help="生成的产品数量")
    parser.add_argument("--all", action="store_true", help="生成所有分类的数据")
    parser.add_argument("--list", action="store_true", help="列出所有可用的分类")
    
    args = parser.parse_args()
    
    # 列出所有可用的分类
    if args.list:
        print("可用的分类:")
        for category in ELECTRONIC_CATEGORIES:
            print(f"- {category['name']}")
        return
    
    # 生成数据
    if args.all:
        logger.info(f"开始生成所有分类的数据，每个分类 {args.num} 条")
        files = generate_all_categories(args.num)
        logger.info(f"所有分类数据生成完成，共生成 {len(files)} 个文件")
    else:
        logger.info(f"开始生成分类 {args.category} 的数据，共 {args.num} 条")
        file_path = generate_and_save(args.category, args.num)
        if file_path:
            logger.info(f"数据生成完成，保存到 {file_path}")

if __name__ == "__main__":
    main()