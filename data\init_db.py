# -*- coding: utf-8 -*-
"""
数据库初始化脚本，用于创建数据库和表结构
"""

import os
import sys
import logging
import MySQLdb as mysql
from dotenv import load_dotenv

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.db_connection import db_manager, Base
from data.models import Category, Product, Shop, RawData, PriceHistory, SalesHistory

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("db_init.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

def create_database():
    """
    创建数据库
    """
    # 获取数据库配置
    db_host = os.getenv("DB_HOST", "localhost")
    db_port = int(os.getenv("DB_PORT", "3306"))
    db_user = os.getenv("DB_USER", "root")
    db_password = os.getenv("DB_PASSWORD", "")
    db_name = os.getenv("DB_NAME", "taobao_electronics")
    
    try:
        # 连接MySQL服务器
        conn = mysql.connect(
            host=db_host,
            port=db_port,
            user=db_user,
            passwd=db_password
        )
        
        cursor = conn.cursor()
        
        # 创建数据库
        cursor.execute(f"CREATE DATABASE IF NOT EXISTS {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        
        logger.info(f"数据库 {db_name} 创建成功")
        
        # 关闭连接
        cursor.close()
        conn.close()
        
        return True
    except Exception as e:
        logger.error(f"创建数据库失败: {str(e)}")
        return False

def create_tables():
    """
    创建表结构
    """
    try:
        # 创建所有表
        Base.metadata.create_all(db_manager.engine)
        
        logger.info("表结构创建成功")
        return True
    except Exception as e:
        logger.error(f"创建表结构失败: {str(e)}")
        return False

def init_categories():
    """
    初始化产品分类
    """
    try:
        # 获取会话
        session = db_manager.get_session()
        
        # 定义电子产品分类
        categories = [
            {"name": "手机", "description": "智能手机、功能手机等"},
            {"name": "笔记本电脑", "description": "各品牌笔记本电脑"},
            {"name": "平板电脑", "description": "各品牌平板电脑"},
            {"name": "智能手表", "description": "智能手表、运动手环等"},
            {"name": "耳机", "description": "有线耳机、无线耳机、蓝牙耳机等"},
            {"name": "智能音箱", "description": "智能音箱、蓝牙音箱等"},
            {"name": "相机", "description": "数码相机、单反相机、微单相机等"},
            {"name": "电视", "description": "智能电视、液晶电视、OLED电视等"},
            {"name": "游戏设备", "description": "游戏主机、游戏手柄等"},
            {"name": "智能家居", "description": "智能门锁、智能灯泡、智能插座等"},
            {"name": "电脑配件", "description": "显示器、键盘、鼠标、硬盘等"}
        ]
        
        # 添加分类
        for category_data in categories:
            # 检查分类是否已存在
            existing = session.query(Category).filter(Category.name == category_data["name"]).first()
            if not existing:
                category = Category(**category_data)
                session.add(category)
        
        # 提交事务
        session.commit()
        
        logger.info("产品分类初始化成功")
        return True
    except Exception as e:
        logger.error(f"初始化产品分类失败: {str(e)}")
        session.rollback()
        return False
    finally:
        db_manager.close_session()

def main():
    """
    主函数
    """
    logger.info("开始初始化数据库...")
    
    # 创建数据库
    if not create_database():
        logger.error("数据库创建失败，初始化终止")
        return False
    
    # 创建表结构
    if not create_tables():
        logger.error("表结构创建失败，初始化终止")
        return False
    
    # 初始化产品分类
    if not init_categories():
        logger.error("产品分类初始化失败，初始化终止")
        return False
    
    logger.info("数据库初始化完成")
    return True

if __name__ == "__main__":
    main()