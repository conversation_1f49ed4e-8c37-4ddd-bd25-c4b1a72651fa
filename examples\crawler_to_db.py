# -*- coding: utf-8 -*-
"""
爬虫数据导入脚本

该脚本用于爬取电商数据并将其导入到数据库中
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入爬虫模块和数据导入模块
from crawler import TaobaoCrawler, ELECTRONIC_CATEGORIES
from data.data_import import import_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("crawler_to_db.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def crawl_and_import(category_name, pages=1, get_details=False):
    """
    爬取指定分类的数据并导入到数据库
    
    Args:
        category_name: 分类名称
        pages: 每个关键词爬取的页数
        get_details: 是否获取详细信息
        
    Returns:
        tuple: (爬取数量, 导入成功数量, 导入失败数量)
    """
    # 查找对应的分类配置
    category = None
    for cat in ELECTRONIC_CATEGORIES:
        if cat["name"] == category_name:
            category = cat
            break
    
    if not category:
        logger.error(f"未找到分类: {category_name}")
        return 0, 0, 0
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/raw")
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建爬虫实例
    crawler = TaobaoCrawler()
    
    try:
        logger.info(f"开始爬取分类: {category_name}")
        
        # 爬取数据
        products = crawler.crawl(
            keywords=category["keywords"],
            pages_per_keyword=pages,
            get_details=get_details
        )
        
        if not products:
            logger.warning(f"未爬取到任何产品数据")
            return 0, 0, 0
        
        # 保存数据到JSON文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(output_dir, f"{category_name}_{timestamp}.json")
        
        with open(filename, 'w', encoding='utf-8') as f:
            import json
            json.dump(products, f, ensure_ascii=False, indent=2)
        
        logger.info(f"成功爬取 {len(products)} 个产品数据并保存到 {filename}")
        
        # 导入数据到数据库
        logger.info(f"开始将数据导入到数据库")
        success, failed = import_data(filename, category_name)
        
        logger.info(f"数据导入完成，成功: {success}，失败: {failed}")
        return len(products), success, failed
            
    except Exception as e:
        logger.error(f"爬取或导入过程中发生错误: {str(e)}")
        return 0, 0, 0
    finally:
        # 关闭爬虫
        crawler.close()

def main():
    """
    主函数
    """
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="淘宝电子产品爬取并导入数据库工具")
    parser.add_argument("--category", default="手机", help="要爬取的分类名称")
    parser.add_argument("--pages", type=int, default=1, help="每个关键词爬取的页数")
    parser.add_argument("--details", action="store_true", help="是否获取详细信息")
    parser.add_argument("--list", action="store_true", help="列出所有可用的分类")
    
    args = parser.parse_args()
    
    # 列出所有可用的分类
    if args.list:
        print("可用的分类:")
        for category in ELECTRONIC_CATEGORIES:
            print(f"- {category['name']}")
        return
    
    # 爬取并导入数据
    crawled, success, failed = crawl_and_import(
        category_name=args.category,
        pages=args.pages,
        get_details=args.details
    )
    
    print(f"爬取完成，共爬取: {crawled}，导入成功: {success}，导入失败: {failed}")

if __name__ == "__main__":
    main()