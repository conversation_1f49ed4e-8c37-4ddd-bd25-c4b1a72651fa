# -*- coding: utf-8 -*-
"""
可视化视图模块，用于展示数据可视化图表
"""

import os
from flask import Blueprint, render_template, request, jsonify, current_app, send_from_directory
from sqlalchemy import func

from web.app import db, cache
from data.models import Product, Category, Shop
from visualization.basic_charts import BasicCharts
from visualization.advanced_charts import AdvancedCharts

# 创建蓝图
visualization_bp = Blueprint('visualization', __name__)


@visualization_bp.route('/')
def index():
    """
    可视化首页
    显示可用的可视化类型
    """
    return render_template('visualization/index.html')


@visualization_bp.route('/basic')
def basic_charts():
    """
    基础图表页面
    显示基础数据可视化图表
    """
    # 获取分类ID参数
    category_id = request.args.get('category_id', None, type=int)
    
    # 获取所有分类
    categories = Category.query.all()
    
    # 如果指定了分类，则获取该分类的信息
    if category_id:
        category = Category.query.get_or_404(category_id)
    else:
        category = None
    
    # 获取图表类型参数
    chart_type = request.args.get('chart_type', 'all')
    
    # 获取可用的图表类型
    available_charts = [
        {'id': 'category_distribution', 'name': '分类分布'},
        {'id': 'price_distribution', 'name': '价格分布'},
        {'id': 'sales_distribution', 'name': '销量分布'},
        {'id': 'top_products', 'name': '热门产品排行'},
        {'id': 'top_shops', 'name': '热门店铺排行'},
        {'id': 'location_distribution', 'name': '地区分布'},
        {'id': 'keyword_distribution', 'name': '关键词分布'},
        {'id': 'price_sales_correlation', 'name': '价格与销量相关性'}
    ]
    
    return render_template(
        'visualization/basic_charts.html',
        categories=categories,
        current_category=category,
        chart_type=chart_type,
        available_charts=available_charts
    )


@visualization_bp.route('/advanced')
def advanced_charts():
    """
    高级图表页面
    显示交互式图表和仪表盘
    """
    # 获取分类ID参数
    category_id = request.args.get('category_id', None, type=int)
    
    # 获取所有分类
    categories = Category.query.all()
    
    # 如果指定了分类，则获取该分类的信息
    if category_id:
        category = Category.query.get_or_404(category_id)
    else:
        category = None
    
    # 获取图表类型参数
    chart_type = request.args.get('chart_type', 'all')
    
    # 获取可用的图表类型
    available_charts = [
        {'id': 'price_trend', 'name': '价格趋势'},
        {'id': 'sales_trend', 'name': '销量趋势'},
        {'id': 'brand_analysis', 'name': '品牌分析'},
        {'id': 'seasonality', 'name': '季节性分析'},
        {'id': 'competition', 'name': '竞争对手分析'},
        {'id': 'keyword_cloud', 'name': '关键词词云'},
        {'id': 'sentiment_analysis', 'name': '情感分析'}
    ]
    
    return render_template(
        'visualization/advanced_charts.html',
        categories=categories,
        current_category=category,
        chart_type=chart_type,
        available_charts=available_charts
    )


@visualization_bp.route('/generate/basic')
def generate_basic_chart():
    """
    生成基础图表
    根据请求参数生成图表并返回图表URL
    """
    # 获取参数
    chart_type = request.args.get('chart_type', 'category_distribution')
    category_id = request.args.get('category_id', None, type=int)
    output_format = request.args.get('format', 'png')
    
    # 创建图表生成器
    charts = BasicCharts()
    
    # 图表保存路径
    charts_folder = current_app.config['CHARTS_FOLDER']
    os.makedirs(charts_folder, exist_ok=True)
    
    # 根据图表类型生成图表
    if chart_type == 'category_distribution':
        filename = charts.plot_category_distribution(save_path=charts_folder, file_format=output_format)
    elif chart_type == 'price_distribution':
        filename = charts.plot_price_distribution(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'sales_distribution':
        filename = charts.plot_sales_distribution(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'top_products':
        filename = charts.plot_top_products(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'top_shops':
        filename = charts.plot_top_shops(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'location_distribution':
        filename = charts.plot_location_distribution(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'keyword_distribution':
        filename = charts.plot_keyword_distribution(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'price_sales_correlation':
        filename = charts.plot_price_sales_correlation(category_id=category_id, save_path=charts_folder, file_format=output_format)
    else:
        return jsonify({'error': '不支持的图表类型'}), 400
    
    # 返回图表URL
    chart_url = url_for('visualization.get_chart', filename=filename)
    return jsonify({'chart_url': chart_url})


@visualization_bp.route('/generate/advanced')
def generate_advanced_chart():
    """
    生成高级图表
    根据请求参数生成交互式图表并返回图表URL
    """
    # 获取参数
    chart_type = request.args.get('chart_type', 'price_trend')
    category_id = request.args.get('category_id', None, type=int)
    output_format = request.args.get('format', 'html')
    
    # 创建图表生成器
    charts = AdvancedCharts()
    
    # 图表保存路径
    charts_folder = current_app.config['CHARTS_FOLDER']
    os.makedirs(charts_folder, exist_ok=True)
    
    # 根据图表类型生成图表
    if chart_type == 'price_trend':
        filename = charts.plot_price_trend(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'sales_trend':
        filename = charts.plot_sales_trend(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'brand_analysis':
        filename = charts.plot_brand_analysis(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'seasonality':
        filename = charts.plot_seasonality(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'competition':
        filename = charts.plot_competition(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'keyword_cloud':
        filename = charts.plot_keyword_cloud(category_id=category_id, save_path=charts_folder, file_format=output_format)
    elif chart_type == 'sentiment_analysis':
        filename = charts.plot_sentiment_analysis(category_id=category_id, save_path=charts_folder, file_format=output_format)
    else:
        return jsonify({'error': '不支持的图表类型'}), 400
    
    # 返回图表URL
    if output_format == 'html':
        chart_url = url_for('visualization.get_interactive_chart', filename=filename)
    else:
        chart_url = url_for('visualization.get_chart', filename=filename)
    
    return jsonify({'chart_url': chart_url})


@visualization_bp.route('/charts/<filename>')
def get_chart(filename):
    """
    获取图表文件
    
    Args:
        filename: 图表文件名
    """
    return send_from_directory(current_app.config['CHARTS_FOLDER'], filename)


@visualization_bp.route('/interactive/<filename>')
def get_interactive_chart(filename):
    """
    获取交互式图表
    
    Args:
        filename: 图表文件名
    """
    return render_template('visualization/interactive_chart.html', chart_file=filename)


@visualization_bp.route('/dashboard')
def dashboard():
    """
    数据可视化仪表盘
    集成多个图表的综合展示页面
    """
    # 获取分类ID参数
    category_id = request.args.get('category_id', None, type=int)
    
    # 获取所有分类
    categories = Category.query.all()
    
    # 如果指定了分类，则获取该分类的信息
    if category_id:
        category = Category.query.get_or_404(category_id)
    else:
        category = None
    
    return render_template(
        'visualization/dashboard.html',
        categories=categories,
        current_category=category
    )