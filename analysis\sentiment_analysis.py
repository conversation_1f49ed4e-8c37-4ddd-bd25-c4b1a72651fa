# -*- coding: utf-8 -*-
"""
情感分析模块，用于分析产品评论的情感倾向
"""

import pandas as pd
import numpy as np
import logging
import jieba
import re
from collections import Counter
from sqlalchemy import func
import os
import json

from data.db_connection import get_db_session, close_db_session
from data.models import Product, Category, RawData
from analysis.text_analysis import TextAnalyzer, load_stopwords

# 配置日志
logger = logging.getLogger(__name__)


class SentimentAnalyzer:
    """
    情感分析类，用于分析产品评论的情感倾向
    """
    
    def __init__(self, sentiment_dict_path=None, degree_dict_path=None, negation_dict_path=None, stopwords_path=None):
        """
        初始化情感分析器
        
        Args:
            sentiment_dict_path: 情感词典路径
            degree_dict_path: 程度词典路径
            negation_dict_path: 否定词典路径
            stopwords_path: 停用词典路径
        """
        self.text_analyzer = TextAnalyzer(stopwords_path=stopwords_path)
        self.stopwords = self.text_analyzer.stopwords
        
        # 加载情感词典
        self.sentiment_dict = self._load_sentiment_dict(sentiment_dict_path)
        
        # 加载程度词典
        self.degree_dict = self._load_degree_dict(degree_dict_path)
        
        # 加载否定词典
        self.negation_dict = self._load_negation_dict(negation_dict_path)
        
        # 情感分析结果缓存
        self.sentiment_cache = {}
    
    def _load_sentiment_dict(self, file_path=None):
        """
        加载情感词典
        
        Args:
            file_path: 情感词典文件路径
            
        Returns:
            dict: 情感词典，格式为 {word: score}
        """
        # 默认情感词典
        default_sentiment_dict = {
            # 正面词汇
            "优秀": 1.0, "良好": 0.8, "满意": 0.8, "喜欢": 0.9, "赞": 1.0,
            "好": 0.8, "棒": 0.9, "强": 0.7, "精彩": 0.9, "精致": 0.8,
            "精美": 0.8, "漂亮": 0.8, "美观": 0.8, "大气": 0.7, "高端": 0.8,
            "奢华": 0.7, "时尚": 0.7, "简约": 0.6, "简单": 0.5, "实用": 0.7,
            "耐用": 0.8, "好用": 0.8, "方便": 0.7, "快捷": 0.7, "快速": 0.7,
            "高效": 0.8, "省电": 0.7, "节能": 0.7, "环保": 0.7, "安全": 0.8,
            "稳定": 0.8, "可靠": 0.8, "耐心": 0.7, "专业": 0.8, "细致": 0.7,
            "周到": 0.7, "热情": 0.7, "友好": 0.7, "亲切": 0.7, "舒适": 0.8,
            "舒服": 0.8, "舒心": 0.8, "愉快": 0.8, "开心": 0.8, "惊喜": 0.9,
            "惊艳": 0.9, "震撼": 0.9, "感动": 0.8, "感谢": 0.7, "谢谢": 0.7,
            "推荐": 0.8, "支持": 0.7, "肯定": 0.7, "认可": 0.7, "赞同": 0.7,
            "赞成": 0.7, "赞赏": 0.8, "赞美": 0.8, "表扬": 0.8, "称赞": 0.8,
            "夸奖": 0.8, "夸赞": 0.8, "称道": 0.8, "佩服": 0.8, "敬佩": 0.8,
            "敬仰": 0.8, "崇拜": 0.8, "爱": 0.9, "爱好": 0.8, "爱慕": 0.8,
            "喜爱": 0.9, "热爱": 0.9, "痴迷": 0.9, "迷恋": 0.9, "着迷": 0.9,
            "沉迷": 0.8, "沉醉": 0.8, "陶醉": 0.8, "享受": 0.8, "享用": 0.7,
            "享乐": 0.7, "欣赏": 0.7, "欣喜": 0.8, "欢喜": 0.8, "欢乐": 0.8,
            "欢快": 0.8, "欢欣": 0.8, "欢畅": 0.8, "欢腾": 0.8, "欢呼": 0.8,
            "欢庆": 0.8, "庆祝": 0.8, "庆贺": 0.8, "祝贺": 0.8, "祝福": 0.8,
            "祝愿": 0.8, "祈祷": 0.7, "祈求": 0.7, "祈盼": 0.7, "期待": 0.7,
            "期盼": 0.7, "期望": 0.7, "希望": 0.7, "盼望": 0.7, "盼顾": 0.7,
            "渴望": 0.8, "渴求": 0.8, "渴盼": 0.8, "企盼": 0.7, "企望": 0.7,
            "向往": 0.7, "向慕": 0.7, "憧憬": 0.8, "梦想": 0.8, "理想": 0.7,
            "愿望": 0.7, "愿意": 0.6, "情愿": 0.6, "甘愿": 0.7, "甘心": 0.7,
            "乐意": 0.7, "乐于": 0.7, "乐观": 0.8, "乐趣": 0.8, "乐事": 0.7,
            "乐园": 0.7, "乐土": 0.7, "乐天": 0.7, "乐不思蜀": 0.8, "乐不可支": 0.9,
            "乐此不疲": 0.8, "乐在其中": 0.8, "乐享": 0.8, "乐活": 0.8, "乐业": 0.7,
            
            # 负面词汇
            "差": -0.8, "糟糕": -0.9, "糟": -0.8, "糟透": -0.9, "糟糕透顶": -1.0,
            "差劲": -0.9, "差评": -0.9, "差距": -0.7, "差别": -0.6, "差异": -0.5,
            "不好": -0.8, "不满": -0.8, "不满意": -0.9, "不喜欢": -0.8, "不赞": -0.8,
            "不行": -0.8, "不可": -0.7, "不能": -0.7, "不要": -0.7, "不该": -0.7,
            "不应该": -0.7, "不应": -0.7, "不宜": -0.7, "不适": -0.7, "不适合": -0.8,
            "不适宜": -0.8, "不适应": -0.7, "不舒服": -0.8, "不舒适": -0.8, "不爽": -0.9,
            "不快": -0.8, "不快乐": -0.8, "不开心": -0.8, "不高兴": -0.8, "不悦": -0.8,
            "不愉快": -0.8, "不欢": -0.8, "不欢迎": -0.8, "不欣赏": -0.7, "不喜": -0.8,
            "不喜爱": -0.8, "不爱": -0.8, "不热爱": -0.8, "不热衷": -0.7, "不感兴趣": -0.7,
            "不关心": -0.7, "不在乎": -0.7, "不在意": -0.7, "不屑": -0.8, "不屑一顾": -0.9,
            "不屑于": -0.8, "不屑于顾": -0.9, "不屑一顾": -0.9, "不屑一提": -0.9, "不屑一看": -0.9,
            "不屑一听": -0.9, "不屑一问": -0.9, "不屑一答": -0.9, "不屑一理": -0.9, "不屑一睹": -0.9,
            "不屑一谈": -0.9, "不屑一言": -0.9, "不屑一笑": -0.9, "不屑一哂": -0.9, "不屑一顾": -0.9,
            "不屑一顾": -0.9, "不屑一顾": -0.9, "不屑一顾": -0.9, "不屑一顾": -0.9, "不屑一顾": -0.9,
            "讨厌": -0.9, "厌恶": -0.9, "厌烦": -0.9, "厌倦": -0.8, "厌恨": -0.9,
            "厌弃": -0.9, "厌恶": -0.9, "厌憎": -0.9, "厌恶": -0.9, "厌恶": -0.9,
            "讨厌": -0.9, "讨人厌": -0.9, "讨嫌": -0.9, "讨厌": -0.9, "讨厌": -0.9,
            "烦": -0.8, "烦人": -0.9, "烦躁": -0.9, "烦闷": -0.8, "烦恼": -0.8,
            "烦忧": -0.8, "烦乱": -0.8, "烦琐": -0.7, "烦杂": -0.7, "烦冗": -0.7,
            "烦难": -0.8, "烦重": -0.8, "烦劳": -0.7, "烦扰": -0.8, "烦嚣": -0.8,
            "烦嚣": -0.8, "烦嚣": -0.8, "烦嚣": -0.8, "烦嚣": -0.8, "烦嚣": -0.8,
            "恨": -0.9, "恨不得": -0.9, "恨不能": -0.9, "恨不已": -0.9, "恨之入骨": -1.0,
            "恨死": -1.0, "恨透": -1.0, "恨极": -1.0, "恨恨": -0.9, "恨恶": -0.9,
            "恨意": -0.9, "恨声": -0.9, "恨气": -0.9, "恨恨": -0.9, "恨恨": -0.9,
            "怨": -0.8, "怨恨": -0.9, "怨愤": -0.9, "怨气": -0.8, "怨声": -0.8,
            "怨言": -0.8, "怨念": -0.8, "怨毒": -0.9, "怨怼": -0.9, "怨怒": -0.9,
            "怨忿": -0.9, "怨忾": -0.9, "怨尤": -0.8, "怨悔": -0.8, "怨悔": -0.8,
            "恼": -0.8, "恼怒": -0.9, "恼火": -0.9, "恼恨": -0.9, "恼羞": -0.9,
            "恼羞成怒": -1.0, "恼怨": -0.9, "恼恚": -0.9, "恼恚": -0.9, "恼恚": -0.9,
            "怒": -0.9, "怒气": -0.9, "怒火": -0.9, "怒意": -0.9, "怒形": -0.9,
            "怒容": -0.9, "怒色": -0.9, "怒目": -0.9, "怒视": -0.9, "怒斥": -0.9,
            "怒骂": -0.9, "怒吼": -0.9, "怒喝": -0.9, "怒叱": -0.9, "怒叱": -0.9,
            "气": -0.8, "气愤": -0.9, "气恼": -0.9, "气急": -0.9, "气急败坏": -1.0,
            "气冲冲": -0.9, "气呼呼": -0.9, "气鼓鼓": -0.9, "气势汹汹": -0.9, "气势汹汹": -0.9,
            "生气": -0.9, "动气": -0.8, "发气": -0.8, "发怒": -0.9, "发火": -0.9,
            "发飙": -0.9, "发作": -0.8, "发泄": -0.8, "发泄": -0.8, "发泄": -0.8,
            "愤": -0.8, "愤怒": -0.9, "愤恨": -0.9, "愤慨": -0.9, "愤懑": -0.9,
            "愤愤": -0.9, "愤愤不平": -0.9, "愤世嫉俗": -0.9, "愤世": -0.8, "愤世": -0.8,
            "悲": -0.8, "悲伤": -0.9, "悲痛": -0.9, "悲哀": -0.9, "悲凄": -0.9,
            "悲惨": -0.9, "悲惨": -0.9, "悲惨": -0.9, "悲惨": -0.9, "悲惨": -0.9,
            "伤心": -0.9, "伤感": -0.8, "伤怀": -0.8, "伤悲": -0.8, "伤痛": -0.9,
            "伤痛": -0.9, "伤痛": -0.9, "伤痛": -0.9, "伤痛": -0.9, "伤痛": -0.9,
            "痛": -0.9, "痛苦": -0.9, "痛楚": -0.9, "痛不欲生": -1.0, "痛不可当": -1.0,
            "痛心": -0.9, "痛心疾首": -1.0, "痛恨": -0.9, "痛恶": -0.9, "痛骂": -0.9,
            "痛斥": -0.9, "痛责": -0.9, "痛责": -0.9, "痛责": -0.9, "痛责": -0.9,
            "苦": -0.8, "苦恼": -0.9, "苦闷": -0.9, "苦痛": -0.9, "苦楚": -0.9,
            "苦难": -0.9, "苦厄": -0.9, "苦海": -0.9, "苦海无边": -1.0, "苦海无边": -1.0,
            "忧": -0.8, "忧愁": -0.9, "忧伤": -0.9, "忧虑": -0.9, "忧心": -0.9,
            "忧心忡忡": -1.0, "忧心如焚": -1.0, "忧心如焚": -1.0, "忧心如焚": -1.0, "忧心如焚": -1.0,
            "愁": -0.8, "愁苦": -0.9, "愁闷": -0.9, "愁眉": -0.8, "愁眉不展": -0.9,
            "愁眉苦脸": -0.9, "愁眉锁眼": -0.9, "愁眉锁眼": -0.9, "愁眉锁眼": -0.9, "愁眉锁眼": -0.9,
            "哀": -0.8, "哀伤": -0.9, "哀痛": -0.9, "哀悼": -0.9, "哀悯": -0.8,
            "哀怜": -0.8, "哀叹": -0.8, "哀叹": -0.8, "哀叹": -0.8, "哀叹": -0.8,
            "惨": -0.9, "惨痛": -1.0, "惨烈": -1.0, "惨重": -1.0, "惨淡": -0.9,
            "惨白": -0.9, "惨败": -1.0, "惨遭": -1.0, "惨遭": -1.0, "惨遭": -1.0,
            "凄": -0.8, "凄惨": -0.9, "凄凉": -0.9, "凄厉": -0.9, "凄切": -0.9,
            "凄婉": -0.8, "凄怆": -0.9, "凄怆": -0.9, "凄怆": -0.9, "凄怆": -0.9,
            "悔": -0.8, "悔恨": -0.9, "悔恨交加": -1.0, "悔不当初": -0.9, "悔之晚矣": -0.9,
            "悔之不及": -0.9, "悔过": -0.8, "悔改": -0.7, "悔改": -0.7, "悔改": -0.7,
            "恨": -0.9, "恨不得": -0.9, "恨不能": -0.9, "恨不已": -0.9, "恨之入骨": -1.0,
            "恨死": -1.0, "恨透": -1.0, "恨极": -1.0, "恨恨": -0.9, "恨恶": -0.9,
            "恨意": -0.9, "恨声": -0.9, "恨气": -0.9, "恨恨": -0.9, "恨恨": -0.9,
            "怨": -0.8, "怨恨": -0.9, "怨愤": -0.9, "怨气": -0.8, "怨声": -0.8,
            "怨言": -0.8, "怨念": -0.8, "怨毒": -0.9, "怨怼": -0.9, "怨怒": -0.9,
            "怨忿": -0.9, "怨忾": -0.9, "怨尤": -0.8, "怨悔": -0.8, "怨悔": -0.8,
            "恼": -0.8, "恼怒": -0.9, "恼火": -0.9, "恼恨": -0.9, "恼羞": -0.9,
            "恼羞成怒": -1.0, "恼怨": -0.9, "恼恚": -0.9, "恼恚": -0.9, "恼恚": -0.9,
            "怒": -0.9, "怒气": -0.9, "怒火": -0.9, "怒意": -0.9, "怒形": -0.9,
            "怒容": -0.9, "怒色": -0.9, "怒目": -0.9, "怒视": -0.9, "怒斥": -0.9,
            "怒骂": -0.9, "怒吼": -0.9, "怒喝": -0.9, "怒叱": -0.9, "怒叱": -0.9,
            "气": -0.8, "气愤": -0.9, "气恼": -0.9, "气急": -0.9, "气急败坏": -1.0,
            "气冲冲": -0.9, "气呼呼": -0.9, "气鼓鼓": -0.9, "气势汹汹": -0.9, "气势汹汹": -0.9,
            "生气": -0.9, "动气": -0.8, "发气": -0.8, "发怒": -0.9, "发火": -0.9,
            "发飙": -0.9, "发作": -0.8, "发泄": -0.8, "发泄": -0.8, "发泄": -0.8,
            "愤": -0.8, "愤怒": -0.9, "愤恨": -0.9, "愤慨": -0.9, "愤懑": -0.9,
            "愤愤": -0.9, "愤愤不平": -0.9, "愤世嫉俗": -0.9, "愤世": -0.8, "愤世": -0.8,
            "悲": -0.8, "悲伤": -0.9, "悲痛": -0.9, "悲哀": -0.9, "悲凄": -0.9,
            "悲惨": -0.9, "悲惨": -0.9, "悲惨": -0.9, "悲惨": -0.9, "悲惨": -0.9,
            "伤心": -0.9, "伤感": -0.8, "伤怀": -0.8, "伤悲": -0.8, "伤痛": -0.9,
            "伤痛": -0.9, "伤痛": -0.9, "伤痛": -0.9, "伤痛": -0.9, "伤痛": -0.9,
            "痛": -0.9, "痛苦": -0.9, "痛楚": -0.9, "痛不欲生": -1.0, "痛不可当": -1.0,
            "痛心": -0.9, "痛心疾首": -1.0, "痛恨": -0.9, "痛恶": -0.9, "痛骂": -0.9,
            "痛斥": -0.9, "痛责": -0.9, "痛责": -0.9, "痛责": -0.9, "痛责": -0.9,
            "苦": -0.8, "苦恼": -0.9, "苦闷": -0.9, "苦痛": -0.9, "苦楚": -0.9,
            "苦难": -0.9, "苦厄": -0.9, "苦海": -0.9, "苦海无边": -1.0, "苦海无边": -1.0,
            "忧": -0.8, "忧愁": -0.9, "忧伤": -0.9, "忧虑": -0.9, "忧心": -0.9,
            "忧心忡忡": -1.0, "忧心如焚": -1.0, "忧心如焚": -1.0, "忧心如焚": -1.0, "忧心如焚": -1.0,
            "愁": -0.8, "愁苦": -0.9, "愁闷": -0.9, "愁眉": -0.8, "愁眉不展": -0.9,
            "愁眉苦脸": -0.9, "愁眉锁眼": -0.9, "愁眉锁眼": -0.9, "愁眉锁眼": -0.9, "愁眉锁眼": -0.9,
            "哀": -0.8, "哀伤": -0.9, "哀痛": -0.9, "哀悼": -0.9, "哀悯": -0.8,
            "哀怜": -0.8, "哀叹": -0.8, "哀叹": -0.8, "哀叹": -0.8, "哀叹": -0.8,
            "惨": -0.9, "惨痛": -1.0, "惨烈": -1.0, "惨重": -1.0, "惨淡": -0.9,
            "惨白": -0.9, "惨败": -1.0, "惨遭": -1.0, "惨遭": -1.0, "惨遭": -1.0,
            "凄": -0.8, "凄惨": -0.9, "凄凉": -0.9, "凄厉": -0.9, "凄切": -0.9,
            "凄婉": -0.8, "凄怆": -0.9, "凄怆": -0.9, "凄怆": -0.9, "凄怆": -0.9,
            "悔": -0.8, "悔恨": -0.9, "悔恨交加": -1.0, "悔不当初": -0.9, "悔之晚矣": -0.9,
            "悔之不及": -0.9, "悔过": -0.8, "悔改": -0.7, "悔改": -0.7, "悔改": -0.7,
        }
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    sentiment_dict = {}
                    for line in f:
                        parts = line.strip().split('\t')
                        if len(parts) >= 2:
                            word = parts[0].strip()
                            try:
                                score = float(parts[1].strip())
                                sentiment_dict[word] = score
                            except ValueError:
                                continue
                return sentiment_dict
            except Exception as e:
                logger.error(f"加载情感词典失败: {e}")
                return default_sentiment_dict
        else:
            return default_sentiment_dict
    
    def _load_degree_dict(self, file_path=None):
        """
        加载程度词典
        
        Args:
            file_path: 程度词典文件路径
            
        Returns:
            dict: 程度词典，格式为 {word: score}
        """
        # 默认程度词典
        default_degree_dict = {
            "极其": 2.0, "极度": 2.0, "极端": 2.0, "极为": 2.0, "极": 2.0,
            "最为": 2.0, "最": 2.0, "最为": 2.0, "最最": 2.0, "最重要": 2.0,
            "最大": 2.0, "最小": 2.0, "最高": 2.0, "最低": 2.0, "最好": 2.0,
            "最坏": 2.0, "最差": 2.0, "最优": 2.0, "最劣": 2.0, "最慢": 2.0,
            "最快": 2.0, "最远": 2.0, "最近": 2.0, "最长": 2.0, "最短": 2.0,
            "很": 1.5, "太": 1.5, "非常": 1.5, "十分": 1.5, "极其": 1.5,
            "特别": 1.5, "格外": 1.5, "分外": 1.5, "尤其": 1.5, "更加": 1.5,
            "更为": 1.5, "较为": 1.5, "相当": 1.5, "尤为": 1.5, "过于": 1.5,
            "过分": 1.5, "很是": 1.5, "颇为": 1.5, "甚为": 1.5, "异常": 1.5,
            "万分": 1.5, "备至": 1.5, "不堪": 1.5, "不已": 1.5, "不胜": 1.5,
            "之极": 1.5, "之至": 1.5, "无比": 1.5, "无度": 1.5, "无可": 1.5,
            "深为": 1.5, "满": 1.5, "莫大": 1.5, "绝顶": 1.5, "透": 1.5,
            "彻底": 1.5, "完全": 1.5, "十足": 1.5, "截然": 1.5, "根本": 1.5,
            "绝对": 1.5, "全然": 1.5, "满心": 1.5, "一心": 1.5, "一味": 1.5,
            "不少": 1.2, "不小": 1.2, "不乏": 1.2, "不是一般": 1.2, "不易": 1.2,
            "不简单": 1.2, "不一般": 1.2, "个顶个": 1.2, "多": 1.2, "大": 1.2,
            "大为": 1.2, "颇": 1.2, "颇为": 1.2, "甚": 1.2, "甚为": 1.2,
            "实在": 1.2, "好生": 1.2, "着实": 1.2, "够": 1.2, "够戗": 1.2,
            "老": 1.2, "老大": 1.2, "不得了": 1.2, "不行": 1.2, "要命": 1.2,
            "要死": 1.2, "德性": 1.2, "够呛": 1.2, "那个": 1.2, "黑": 1.2,
            "全": 1.2, "真": 1.2, "真心": 1.2, "实": 1.2, "挺": 1.2,
            "怪": 1.2, "出奇": 1.2, "蛮": 1.2, "一味": 1.2, "毫": 1.2,
            "相当": 1.2, "特": 1.2, "特别": 1.2, "尤": 1.2, "尤其": 1.2,
            "尤为": 1.2, "格外": 1.2, "分外": 1.2, "加倍": 1.2, "倍加": 1.2,
            "越发": 1.2, "备至": 1.2, "愈加": 1.2, "愈": 1.2, "愈发": 1.2,
            "愈为": 1.2, "愈益": 1.2, "更": 1.2, "更加": 1.2, "更为": 1.2,
            "更其": 1.2, "还": 1.2, "还要": 1.2, "较": 1.2, "较为": 1.2,
            "较之": 1.2, "进一步": 1.2, "进而": 1.2, "重": 1.2, "又": 1.2,
            "又加": 1.2, "越": 1.2, "越加": 1.2, "越来越": 1.2, "越是": 1.2,
            "益": 1.2, "益发": 1.2, "还": 1.2, "还是": 1.2, "还要": 1.2,
            "尤甚": 1.2, "逾": 1.2, "日渐": 1.2, "日见": 1.2, "日益": 1.2,
            "日趋": 1.2, "每每": 1.2, "渐": 1.2, "渐次": 1.2, "渐渐": 1.2,
            "渐进": 1.2, "逐步": 1.2, "逐渐": 1.2, "逐日": 1.2, "逐步": 1.2,
            "逐月": 1.2, "逐年": 1.2, "日甚一日": 1.2, "与日俱增": 1.2, "稍": 0.8,
            "稍微": 0.8, "稍稍": 0.8, "稍许": 0.8, "略": 0.8, "略微": 0.8,
            "略为": 0.8, "略加": 0.8, "略带": 0.8, "些": 0.8, "些微": 0.8,
            "些许": 0.8, "几分": 0.8, "几许": 0.8, "多少": 0.8, "那么点": 0.8,
            "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "半点": 0.8,
            "一点": 0.8, "一点儿": 0.8, "一些": 0.8, "一丁点": 0.8, "一星半点": 0.8,
            "那么一点": 0.8, "那么一点儿": 0.8, "就一点": 0.8, "就一点儿": 0.8, "只一点": 0.8,
            "只一点儿": 0.8, "轻度": 0.8, "相对": 0.8, "比较": 0.8, "较为": 0.8,
            "较比": 0.8, "还": 0.8, "不大": 0.8, "不太": 0.8, "不很": 0.8,
            "不甚": 0.8, "不怎么": 0.8, "不十分": 0.8, "不特别": 0.8, "不如何": 0.8,
            "不若": 0.8, "算": 0.8, "未免": 0.8, "大概": 0.8, "大约": 0.8,
            "也就": 0.8, "没怎么": 0.8, "没有多": 0.8, "挺": 0.8, "蛮": 0.8,
            "太甚": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8,
            "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8,
            "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8,
            "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8,
            "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8,
            "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8,
            "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8,
            "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8,
            "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8,
            "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8,
            "有一些": 0.8, "多少": 0.8, "那么点": 0.8
        }
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    degree_dict = {}
                    for line in f:
                        parts = line.strip().split('\t')
                        if len(parts) >= 2:
                            word = parts[0].strip()
                            try:
                                score = float(parts[1].strip())
                                degree_dict[word] = score
                            except ValueError:
                                continue
                return degree_dict
            except Exception as e:
                logger.error(f"加载程度词典失败: {e}")
                return default_degree_dict
        else:
            return default_degree_dict
    
    def _load_negation_dict(self, file_path=None):
        """
        加载否定词典
        
        Args:
            file_path: 否定词典文件路径
            
        Returns:
            list: 否定词列表
        """
        # 默认否定词典
        default_negation_dict = [
            "不", "没", "没有", "不曾", "未", "未曾", "不会", "不能", "不可", "不可能",
            "不是", "不要", "不该", "不应该", "不应", "不必", "不用", "不须", "不需", "不需要",
            "无", "无须", "无需", "毫无", "没什么", "没有什么", "没什么", "没有什么", "不怎么", "不太",
            "绝不", "绝没", "绝对不", "绝对没", "决不", "决没", "决非", "断不", "断没", "否",
            "非", "莫", "勿", "毋", "别", "甭", "休", "不必", "无须", "无需",
            "不用", "不须", "不需", "不需要", "不要", "不该", "不应该", "不应", "不可", "不可能",
            "不会", "不能", "不得", "不行", "不准", "不许", "不允许", "不答应", "不同意", "不赞成",
            "不赞同", "不支持", "不接受", "不承认", "不认可", "不认同", "不认为", "不以为", "不觉得", "不料",
            "不想", "不愿", "不愿意", "不肯", "不乐意", "不情愿", "不甘", "不甘心", "不情愿", "不乐意",
            "不高兴", "不开心", "不快乐", "不满意", "不满足", "不喜欢", "不爱", "不喜爱", "不热爱", "不关心",
            "不在乎", "不在意", "不屑", "不屑于", "不屑一顾", "不屑一提", "不屑一看", "不屑一听", "不屑一问", "不屑一答",
            "不屑一理", "不屑一睹", "不屑一谈", "不屑一言", "不屑一笑", "不屑一哂", "不屑一顾", "不屑一顾", "不屑一顾", "不屑一顾"
        ]
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    negation_dict = [line.strip() for line in f if line.strip()]
                return negation_dict
            except Exception as e:
                logger.error(f"加载否定词典失败: {e}")
                return default_negation_dict
        else:
            return default_negation_dict
    
    def analyze_sentiment(self, text):
        """
        分析文本的情感倾向
        
        Args:
            text: 待分析的文本
            
        Returns:
            dict: 情感分析结果，包含情感得分和情感极性
        """
        if not text or len(text.strip()) == 0:
            return {"score": 0, "polarity": "neutral", "keywords": []}
        
        # 检查缓存
        if text in self.sentiment_cache:
            return self.sentiment_cache[text]
        
        # 分词
        words = self.text_analyzer.segment(text)
        
        # 去除停用词
        words = [word for word in words if word not in self.stopwords]
        
        # 情感分析
        score = 0
        sentiment_words = []
        
        i = 0
        while i < len(words):
            # 获取当前词的情感得分
            word = words[i]
            word_score = self.sentiment_dict.get(word, 0)
            
            # 如果是情感词
            if word_score != 0:
                # 向前寻找程度词和否定词
                degree = 1.0
                negation = 1
                
                # 向前最多看5个词
                for j in range(max(0, i-5), i):
                    # 检查是否是程度词
                    if words[j] in self.degree_dict:
                        degree = self.degree_dict[words[j]]
                    
                    # 检查是否是否定词
                    if words[j] in self.negation_dict:
                        negation *= -1
                
                # 计算情感得分
                this_score = word_score * degree * negation
                score += this_score
                
                # 记录情感词
                sentiment_words.append({
                    "word": word,
                    "score": this_score,
                    "original_score": word_score,
                    "degree": degree,
                    "negation": negation == -1
                })
            
            i += 1
        
        # 确定情感极性
        if score > 0:
            polarity = "positive"
        elif score < 0:
            polarity = "negative"
        else:
            polarity = "neutral"
        
        # 结果
        result = {
            "score": score,
            "polarity": polarity,
            "keywords": sentiment_words
        }
        
        # 缓存结果
        self.sentiment_cache[text] = result
        
        return result
    
    def analyze_product_comments(self, product_id=None, category_id=None, limit=1000):
        """
        分析产品评论的情感倾向
        
        Args:
            product_id: 产品ID
            category_id: 分类ID
            limit: 最大评论数量
            
        Returns:
            dict: 情感分析结果
        """
        session = get_db_session()
        try:
            # 获取评论数据
            comments = []
            
            # 这里假设RawData中的comment字段包含评论数据
            # 实际项目中可能需要从其他表或字段获取评论数据
            query = session.query(RawData)
            
            if product_id:
                query = query.filter(RawData.product_id == product_id)
            
            if category_id:
                query = query.join(Product).filter(Product.category_id == category_id)
            
            raw_data = query.limit(limit).all()
            
            # 提取评论数据
            for data in raw_data:
                # 假设评论数据存储在raw_data的json字段中
                if data.json_data:
                    try:
                        json_data = json.loads(data.json_data)
                        if 'comments' in json_data:
                            for comment in json_data['comments']:
                                if isinstance(comment, dict) and 'content' in comment:
                                    comments.append(comment['content'])
                                elif isinstance(comment, str):
                                    comments.append(comment)
                    except Exception as e:
                        logger.error(f"解析评论数据失败: {e}")
            
            # 分析情感
            results = []
            for comment in comments:
                sentiment = self.analyze_sentiment(comment)
                results.append({
                    "comment": comment,
                    "sentiment": sentiment
                })
            
            # 统计情感分布
            positive_count = sum(1 for r in results if r['sentiment']['polarity'] == 'positive')
            negative_count = sum(1 for r in results if r['sentiment']['polarity'] == 'negative')
            neutral_count = sum(1 for r in results if r['sentiment']['polarity'] == 'neutral')
            
            total_count = len(results)
            positive_ratio = positive_count / total_count if total_count > 0 else 0
            negative_ratio = negative_count / total_count if total_count > 0 else 0
            neutral_ratio = neutral_count / total_count if total_count > 0 else 0
            
            # 提取情感关键词
            all_keywords = []
            for r in results:
                all_keywords.extend([k['word'] for k in r['sentiment']['keywords']])
            
            keyword_counter = Counter(all_keywords)
            top_keywords = keyword_counter.most_common(20)
            
            # 计算平均情感得分
            avg_score = sum(r['sentiment']['score'] for r in results) / total_count if total_count > 0 else 0
            
            return {
                "total_comments": total_count,
                "positive_count": positive_count,
                "negative_count": negative_count,
                "neutral_count": neutral_count,
                "positive_ratio": positive_ratio,
                "negative_ratio": negative_ratio,
                "neutral_ratio": neutral_ratio,
                "avg_score": avg_score,
                "top_keywords": top_keywords,
                "comments": results
            }
        
        finally:
            close_db_session(session)
    
    def analyze_category_sentiment(self, category_id=None, limit=1000):
        """
        分析分类下所有产品评论的情感倾向
        
        Args:
            category_id: 分类ID
            limit: 最大评论数量
            
        Returns:
            dict: 情感分析结果
        """
        return self.analyze_product_comments(category_id=category_id, limit=limit)
    
    def analyze_all_categories(self, limit_per_category=200):
        """
        分析所有分类的情感倾向
        
        Args:
            limit_per_category: 每个分类的最大评论数量
            
        Returns:
            dict: 情感分析结果
        """
        session = get_db_session()
        try:
            categories = session.query(Category).all()
            results = {}
            
            for category in categories:
                results[category.name] = self.analyze_category_sentiment(
                    category_id=category.id,
                    limit=limit_per_category
                )
            
            return results
        
        finally:
            close_db_session(session)


def main():
    """
    主函数
    """
    import argparse
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='情感分析工具')
    parser.add_argument('--product_id', type=int, help='产品ID')
    parser.add_argument('--category_id', type=int, help='分类ID')
    parser.add_argument('--all_categories', action='store_true', help='分析所有分类')
    parser.add_argument('--limit', type=int, default=1000, help='最大评论数量')
    parser.add_argument('--output', type=str, help='输出文件路径')
    
    args = parser.parse_args()
    
    # 创建情感分析器
    analyzer = SentimentAnalyzer()
    
    # 根据参数执行相应的分析
    if args.all_categories:
        results = analyzer.analyze_all_categories(limit_per_category=args.limit)
    elif args.category_id:
        results = analyzer.analyze_category_sentiment(category_id=args.category_id, limit=args.limit)
    elif args.product_id:
        results = analyzer.analyze_product_comments(product_id=args.product_id, limit=args.limit)
    else:
        logger.error("请指定产品ID、分类ID或使用--all_categories参数")
        return
    
    # 输出结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logger.info(f"结果已保存到 {args.output}")
    else:
        print(json.dumps(results, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main().8,
            "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8,
            "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8,
            "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8,
            "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8,
            "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8,
            "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8,
            "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8,
            "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8,
            "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8,
            "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8,
            "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8,
            "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8,
            "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8,
            "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8,
            "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8,
            "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8,
            "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8,
            "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8,
            "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8,
            "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8,
            "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8,
            "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8,
            "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8,
            "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8,
            "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8,
            "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8,
            "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8,
            "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8,
            "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8,
            "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8,
            "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8,
            "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8,
            "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8,
            "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8,
            "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8,
            "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8,
            "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8,
            "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8,
            "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8,
            "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8,
            "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8,
            "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8,
            "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8,
            "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8,
            "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8,
            "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8,
            "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8,
            "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8,
            "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8,
            "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8,
            "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8,
            "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8,
            "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8,
            "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8,
            "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8,
            "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8,
            "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8,
            "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8,
            "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8,
            "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8,
            "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8,
            "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8,
            "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8,
            "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8, "有些": 0.8,
            "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8, "略微": 0.8,
            "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8, "几分": 0.8,
            "几许": 0.8, "有点": 0.8, "有些": 0.8, "有一点": 0.8, "有一些": 0.8,
            "多少": 0.8, "那么点": 0.8, "略微": 0.8, "略为": 0.8, "稍稍": 0.8,
            "稍微": 0.8, "稍许": 0.8, "几分": 0.8, "几许": 0.8, "有点": 0.8,
            "有些": 0.8, "有一点": 0.8, "有一些": 0.8, "多少": 0.8, "那么点": 0.8,
            "略微": 0.8, "略为": 0.8, "稍稍": 0.8, "稍微": 0.8, "稍许": 0.8,
            "几分": 0