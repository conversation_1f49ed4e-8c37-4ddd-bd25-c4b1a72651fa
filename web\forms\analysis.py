# -*- coding: utf-8 -*-
"""
分析表单模块，用于处理数据分析相关的表单
"""

from flask_wtf import FlaskForm
from wtforms import SelectField, DateField, SubmitField, IntegerField
from wtforms.validators import DataRequired, Optional, NumberRange

from data.models import Category


class AnalysisFilterForm(FlaskForm):
    """
    分析过滤表单
    用于过滤分析数据
    """
    category_id = SelectField('分类', coerce=int, validators=[Optional()])
    start_date = DateField('开始日期', format='%Y-%m-%d', validators=[Optional()])
    end_date = DateField('结束日期', format='%Y-%m-%d', validators=[Optional()])
    submit = SubmitField('应用')
    
    def __init__(self, *args, **kwargs):
        super(AnalysisFilterForm, self).__init__(*args, **kwargs)
        # 动态加载分类选项
        self.category_id.choices = [(0, '所有分类')] + [
            (category.id, category.name) for category in Category.query.all()
        ]


class TextAnalysisForm(FlaskForm):
    """
    文本分析表单
    用于文本分析参数设置
    """
    category_id = SelectField('分类', coerce=int, validators=[Optional()])
    top_n = IntegerField('显示前N个结果', validators=[Optional(), NumberRange(min=1, max=100)], default=20)
    submit = SubmitField('分析')
    
    def __init__(self, *args, **kwargs):
        super(TextAnalysisForm, self).__init__(*args, **kwargs)
        # 动态加载分类选项
        self.category_id.choices = [(0, '所有分类')] + [
            (category.id, category.name) for category in Category.query.all()
        ]


class SentimentAnalysisForm(FlaskForm):
    """
    情感分析表单
    用于情感分析参数设置
    """
    category_id = SelectField('分类', coerce=int, validators=[Optional()])
    submit = SubmitField('分析')
    
    def __init__(self, *args, **kwargs):
        super(SentimentAnalysisForm, self).__init__(*args, **kwargs)
        # 动态加载分类选项
        self.category_id.choices = [(0, '所有分类')] + [
            (category.id, category.name) for category in Category.query.all()
        ]