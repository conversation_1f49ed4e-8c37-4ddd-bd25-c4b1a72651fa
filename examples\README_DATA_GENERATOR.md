# 电商数据生成与分析指南

本指南将帮助您使用数据生成器脚本生成模拟的电商产品数据，并进行后续的数据导入、分析和可视化展示。

## 1. 环境准备

确保您已安装所有必要的依赖：

```bash
pip install -r requirements.txt
```

## 2. 数据生成与导入

我们提供了两个主要脚本：

- `data_generator.py`: 生成模拟的电商产品数据
- `generate_and_import.py`: 生成数据并将其导入到数据库

### 2.1 生成模拟数据

您可以使用以下命令生成模拟数据：

```bash
# 生成指定分类的数据
python -m examples.data_generator --category 手机 --num 100

# 生成所有分类的数据
python -m examples.data_generator --all --num 50

# 列出所有可用的分类
python -m examples.data_generator --list
```

生成的数据将保存在 `data/raw` 目录下，以JSON格式存储。

### 2.2 生成并导入数据

您可以使用以下命令生成数据并将其导入到数据库：

```bash
# 生成并导入指定分类的数据
python -m examples.generate_and_import --category 手机 --num 100

# 生成并导入所有分类的数据
python -m examples.generate_and_import --all --num 50

# 列出所有可用的分类
python -m examples.generate_and_import --list
```

## 3. 数据分析

完成数据导入后，您可以运行数据分析脚本：

```bash
python -m examples.data_analysis_example
```

该脚本将对导入的数据进行分析，并生成各种统计结果和可视化图表，保存在 `data/analysis` 目录下。

## 4. 启动Web仪表盘

最后，您可以启动Web仪表盘，以交互式方式查看数据分析结果：

```bash
python run_dashboard.py
```

默认情况下，仪表盘将在 http://127.0.0.1:5000 上运行。您可以通过以下参数自定义主机和端口：

```bash
python run_dashboard.py --host 0.0.0.0 --port 8080
```

## 5. 完整流程示例

以下是一个完整的数据生成、分析和可视化流程示例：

```bash
# 1. 生成并导入所有分类的数据
python -m examples.generate_and_import --all --num 50

# 2. 运行数据分析
python -m examples.data_analysis_example

# 3. 启动Web仪表盘
python run_dashboard.py
```

## 6. 注意事项

- 数据生成器会创建模拟的电商产品数据，包括产品ID、标题、价格、销量、详情页URL、店铺信息、位置、爬取时间、属性、评分和评论数量等信息。
- 生成的数据结构与真实爬虫爬取的数据结构保持一致，可以无缝对接现有的数据导入、分析和可视化模块。
- 如果您需要自定义生成的数据，可以修改 `data_generator.py` 文件中的相关配置。

## 7. 数据库重新初始化

如果您遇到数据库关系映射问题（例如 "Could not determine join condition between parent/child tables"），可以使用以下脚本重新初始化数据库并导入数据：

```bash
# 使用批处理文件重新初始化数据库并导入数据
reinitialize_db.bat

# 或者直接运行Python脚本
python -m examples.reinitialize_and_import
```

您还可以使用以下参数自定义重新初始化过程：

```bash
# 重新初始化数据库并导入指定分类的数据
python -m examples.reinitialize_and_import --categories 手机,笔记本电脑

# 指定每个关键词生成的产品数量
python -m examples.reinitialize_and_import --count 30

# 跳过数据库初始化步骤，只重新生成和导入数据
python -m examples.reinitialize_and_import --skip-init

# 跳过数据生成步骤，只重新初始化数据库并导入已有的数据
python -m examples.reinitialize_and_import --skip-generate
```

## 8. 故障排除

如果您在运行过程中遇到问题，请检查以下几点：

1. 确保已安装所有必要的依赖
2. 确保数据库配置正确
3. 检查日志文件（`generate_and_import.log`、`data_analysis.log`、`dashboard.log`、`reinitialize_and_import.log`）以获取详细的错误信息
4. 如果遇到数据库关系映射问题，请使用上述的重新初始化脚本

如果问题仍然存在，请联系开发团队获取支持。