# 淘宝电子产品数据爬取与分析系统

## 项目概述

本系统旨在爬取淘宝电子产品销售数据，并通过数据分析和可视化展示市场趋势、消费者偏好、品牌竞争等多维度信息，为电子产品销售决策提供数据支持。

## 系统架构

系统采用Python作为主要开发语言，基于Selenium实现数据爬取，MySQL进行数据存储，并结合数据分析与可视化技术展示分析结果。

### 主要模块

1. **数据爬取模块**：基于Selenium的淘宝电子产品数据爬取
2. **数据存储模块**：MySQL数据库设计与数据管理
3. **数据分析模块**：基于pandas、numpy等的数据分析
4. **数据可视化模块**：基于matplotlib、seaborn、pyecharts等的数据可视化
5. **Web展示模块**：基于Flask的分析结果展示系统

## 项目结构

```
毕业设计/
├── crawler/                 # 爬虫模块
│   ├── __init__.py         # 模块初始化文件
│   ├── base_crawler.py     # 爬虫基类
│   ├── taobao_crawler.py   # 淘宝爬虫实现
│   ├── user_behavior.py    # 用户行为模拟
│   ├── config.py           # 爬虫配置
│   ├── main.py             # 爬虫主程序
│   └── README.md           # 爬虫模块说明文档
├── data/                   # 数据处理模块
│   ├── __init__.py         # 模块初始化文件
│   ├── models.py           # 数据模型定义
│   ├── db_connection.py    # 数据库连接管理
│   ├── data_import.py      # 数据导入功能
│   ├── data_analysis.py    # 数据分析功能
│   └── data_visualization.py # 数据可视化功能
├── examples/               # 示例脚本
│   ├── crawler_example.py  # 爬虫使用示例
│   ├── crawler_to_db.py    # 爬取并导入数据库示例
│   ├── data_analysis_example.py # 数据分析示例
│   ├── data_visualization_example.py # 数据可视化示例
│   └── web_dashboard.py    # Web仪表盘示例
└── templates/              # Web模板
    └── dashboard.html      # 仪表盘HTML模板
```

## 功能模块详细说明

### 1. 爬虫模块 (crawler)

爬虫模块负责从淘宝平台爬取电子产品数据，主要包括以下组件：

- **BaseCrawler**: 爬虫基类，定义了爬虫的基本接口和通用方法
- **TaobaoCrawler**: 淘宝爬虫实现，能够爬取淘宝电子产品数据
- **UserBehavior**: 用户行为模拟，模拟真实用户的浏览行为，避免被反爬
- **配置常量**: 预定义的电子产品分类、每个关键词的页数、是否获取详情等配置

详细信息请参考 [爬虫模块说明文档](crawler/README.md)。

### 2. 数据处理模块 (data)

数据处理模块负责数据的存储、导入、分析和可视化，主要包括以下组件：

- **数据模型 (models.py)**: 定义了数据库表结构，包括产品、分类、店铺、价格历史、销量历史等
- **数据库连接 (db_connection.py)**: 管理数据库连接和会话
- **数据导入 (data_import.py)**: 将爬取的JSON数据导入到数据库中
- **数据分析 (data_analysis.py)**: 提供多种数据分析功能，如产品数量统计、价格分布、热门产品等
- **数据可视化 (data_visualization.py)**: 生成各种可视化图表，如价格分布图、价格趋势图、分类统计图等

### 3. 示例脚本 (examples)

示例脚本展示了如何使用系统的各个功能模块：

- **crawler_example.py**: 展示如何使用爬虫模块爬取电商数据
- **crawler_to_db.py**: 展示如何爬取数据并导入到数据库中
- **data_analysis_example.py**: 展示如何使用数据分析功能
- **data_visualization_example.py**: 展示如何生成数据可视化图表
- **web_dashboard.py**: 展示如何创建Web仪表盘展示分析结果

## 数据规模

计划爬取11个电子产品分类，每个分类约5000条数据，总计超过5万条数据。

## 安装与配置

### 依赖安装

```bash
pip install -r requirements.txt
```

### 环境变量配置

创建 `.env` 文件，配置以下环境变量：

```
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=ecommerce_data

# 淘宝账号配置（可选）
TAOBAO_USERNAME=your_username
TAOBAO_PASSWORD=your_password
```

### 数据库初始化

```bash
python -c "from data.db_connection import init_db; init_db()"
```

## 使用示例

### 1. 爬取电商数据

```bash
python examples/crawler_example.py --category "手机" --pages 2
```

### 2. 爬取并导入数据库

```bash
python examples/crawler_to_db.py --category "手机" --pages 2
```

### 3. 数据分析

```bash
python examples/data_analysis_example.py --category "手机" --days 30
```

### 4. 数据可视化

```bash
python examples/data_visualization_example.py --category "手机" --days 30
```

### 5. 启动Web仪表盘

```bash
python examples/web_dashboard.py --host 127.0.0.1 --port 5000
```

然后在浏览器中访问 http://127.0.0.1:5000 查看仪表盘。

## 开发进度

- [x] 环境搭建
- [x] 爬虫核心模块开发
- [x] 测试与优化
- [ ] 全量数据爬取
- [x] 数据分析与可视化
- [x] Web系统开发
- [ ] 测试与文档