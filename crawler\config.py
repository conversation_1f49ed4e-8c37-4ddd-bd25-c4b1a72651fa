# -*- coding: utf-8 -*-
"""
爬虫配置文件，定义爬取的电子产品分类和关键词
"""

# 电子产品分类及关键词配置
ELECTRONIC_CATEGORIES = [
    {
        "name": "手机",
        "keywords": ["智能手机", "iPhone", "华为手机", "小米手机", "OPPO手机", "vivo手机", "三星手机", "荣耀手机", "一加手机", "realme手机"]
    },
    {
        "name": "笔记本电脑",
        "keywords": ["笔记本电脑", "轻薄本", "游戏本", "商务本", "MacBook", "联想笔记本", "华为笔记本", "戴尔笔记本", "惠普笔记本", "华硕笔记本"]
    },
    {
        "name": "平板电脑",
        "keywords": ["平板电脑", "iPad", "华为平板", "小米平板", "三星平板", "联想平板", "微软Surface", "荣耀平板", "安卓平板", "二合一平板"]
    },
    {
        "name": "智能手表",
        "keywords": ["智能手表", "Apple Watch", "华为手表", "小米手表", "OPPO手表", "三星手表", "运动手表", "健康手表", "儿童手表", "智能手环"]
    },
    {
        "name": "耳机",
        "keywords": ["无线耳机", "蓝牙耳机", "降噪耳机", "AirPods", "华为耳机", "小米耳机", "OPPO耳机", "vivo耳机", "游戏耳机", "运动耳机"]
    },
    {
        "name": "智能音箱",
        "keywords": ["智能音箱", "蓝牙音箱", "小爱音箱", "天猫精灵", "百度音箱", "华为音箱", "JBL音箱", "哈曼卡顿", "索尼音箱", "Marshall音箱"]
    },
    {
        "name": "相机",
        "keywords": ["数码相机", "单反相机", "微单相机", "佳能相机", "尼康相机", "索尼相机", "富士相机", "松下相机", "运动相机", "GoPro"]
    },
    {
        "name": "电视",
        "keywords": ["智能电视", "4K电视", "OLED电视", "小米电视", "华为电视", "海信电视", "索尼电视", "三星电视", "TCL电视", "创维电视"]
    },
    {
        "name": "游戏设备",
        "keywords": ["游戏主机", "PlayStation", "Xbox", "任天堂Switch", "游戏手柄", "游戏鼠标", "游戏键盘", "游戏耳机", "游戏显示器", "VR设备"]
    },
    {
        "name": "智能家居",
        "keywords": ["智能家居", "智能门锁", "智能灯泡", "智能插座", "智能窗帘", "智能摄像头", "智能门铃", "智能扫地机", "智能净化器", "智能温控"]
    },
    {
        "name": "电脑配件",
        "keywords": ["电脑显示器", "机械键盘", "电竞鼠标", "电脑硬盘", "固态硬盘", "内存条", "显卡", "CPU处理器", "电脑主板", "电源适配器"]
    }
]

# 每个分类爬取的页数
PAGES_PER_KEYWORD = 2

# 是否获取详细信息
GET_DETAILS = True

# 爬取间隔配置（秒）
MIN_DELAY = 2
MAX_DELAY = 5

# 每个分类爬取的最大商品数
MAX_PRODUCTS_PER_CATEGORY = 500