{% extends "layouts/base.html" %}

{% block title %}关于 - 电商数据分析系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <h1 class="mb-4"><i class="fas fa-info-circle me-2"></i>关于电商数据分析系统</h1>
                <p class="lead">电商数据分析系统是一个专为电子商务领域设计的综合数据分析平台，旨在帮助用户深入了解产品市场、消费者行为和销售趋势。</p>
            </div>
        </div>
    </div>

    <!-- 系统介绍 -->
    <div class="col-md-8 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h4 class="mb-0"><i class="fas fa-project-diagram me-2"></i>系统介绍</h4>
            </div>
            <div class="card-body">
                <h5>项目背景</h5>
                <p>随着电子商务的快速发展，海量的产品数据和用户行为数据被不断产生。如何从这些数据中提取有价值的信息，帮助商家和消费者做出更明智的决策，成为了一个重要的研究课题。本系统正是基于这一背景开发的，旨在提供全面的电商数据分析解决方案。</p>
                
                <h5>系统目标</h5>
                <p>本系统的主要目标是通过对电商产品数据的收集、清洗、分析和可视化，帮助用户：</p>
                <ul>
                    <li>了解产品市场分布和竞争情况</li>
                    <li>发现产品价格和销量的关系及变化趋势</li>
                    <li>分析消费者评论和情感倾向</li>
                    <li>识别潜在的市场机会和风险</li>
                    <li>辅助商业决策和营销策略制定</li>
                </ul>
                
                <h5>技术架构</h5>
                <p>本系统采用现代化的技术栈，包括：</p>
                <ul>
                    <li><strong>后端框架：</strong>Python Flask</li>
                    <li><strong>数据库：</strong>SQLite/MySQL</li>
                    <li><strong>数据分析：</strong>Pandas, NumPy, Scikit-learn</li>
                    <li><strong>自然语言处理：</strong>NLTK, jieba</li>
                    <li><strong>数据可视化：</strong>Matplotlib, Seaborn, Plotly</li>
                    <li><strong>前端框架：</strong>Bootstrap 5, jQuery</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 功能特点 -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h4 class="mb-0"><i class="fas fa-star me-2"></i>功能特点</h4>
            </div>
            <div class="card-body">
                <div class="d-flex mb-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-database fa-2x text-primary"></i>
                    </div>
                    <div class="ms-3">
                        <h5>数据管理</h5>
                        <p>支持多种数据源导入，自动数据清洗和标准化，确保数据质量。</p>
                    </div>
                </div>
                
                <div class="d-flex mb-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-bar fa-2x text-success"></i>
                    </div>
                    <div class="ms-3">
                        <h5>数据分析</h5>
                        <p>提供基础统计分析、高级数据挖掘、文本分析和情感分析等功能。</p>
                    </div>
                </div>
                
                <div class="d-flex mb-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-pie fa-2x text-info"></i>
                    </div>
                    <div class="ms-3">
                        <h5>数据可视化</h5>
                        <p>支持多种图表类型，包括静态图表和交互式图表，直观展示分析结果。</p>
                    </div>
                </div>
                
                <div class="d-flex mb-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-tachometer-alt fa-2x text-warning"></i>
                    </div>
                    <div class="ms-3">
                        <h5>仪表盘</h5>
                        <p>提供可定制的数据仪表盘，实时监控关键指标和业务状况。</p>
                    </div>
                </div>
                
                <div class="d-flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-file-export fa-2x text-danger"></i>
                    </div>
                    <div class="ms-3">
                        <h5>报表导出</h5>
                        <p>支持将分析结果和图表导出为多种格式，方便分享和报告生成。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 开发团队 -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-light">
                <h4 class="mb-0"><i class="fas fa-users me-2"></i>开发团队</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-4 text-center">
                        <img src="{{ url_for('static', filename='img/avatar-placeholder.png') }}" class="rounded-circle mb-3" alt="开发者头像" width="150" height="150">
                        <h5>张三</h5>
                        <p class="text-muted">项目负责人</p>
                        <p>负责项目整体规划和架构设计</p>
                    </div>
                    <div class="col-md-4 mb-4 text-center">
                        <img src="{{ url_for('static', filename='img/avatar-placeholder.png') }}" class="rounded-circle mb-3" alt="开发者头像" width="150" height="150">
                        <h5>李四</h5>
                        <p class="text-muted">后端开发工程师</p>
                        <p>负责系统后端和数据分析模块开发</p>
                    </div>
                    <div class="col-md-4 mb-4 text-center">
                        <img src="{{ url_for('static', filename='img/avatar-placeholder.png') }}" class="rounded-circle mb-3" alt="开发者头像" width="150" height="150">
                        <h5>王五</h5>
                        <p class="text-muted">前端开发工程师</p>
                        <p>负责系统前端和数据可视化模块开发</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 联系我们 -->
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-light">
                <h4 class="mb-0"><i class="fas fa-envelope me-2"></i>联系我们</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <form>
                            <div class="mb-3">
                                <label for="name" class="form-label">姓名</label>
                                <input type="text" class="form-control" id="name" placeholder="请输入您的姓名">
                            </div>
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <input type="email" class="form-control" id="email" placeholder="请输入您的邮箱">
                            </div>
                            <div class="mb-3">
                                <label for="message" class="form-label">留言</label>
                                <textarea class="form-control" id="message" rows="5" placeholder="请输入您的留言内容"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">提交留言</button>
                        </form>
                    </div>
                    <div class="col-md-6">
                        <h5>联系方式</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-envelope me-2"></i>邮箱：<EMAIL></li>
                            <li class="mb-2"><i class="fas fa-phone me-2"></i>电话：+86 123 4567 8901</li>
                            <li class="mb-2"><i class="fas fa-map-marker-alt me-2"></i>地址：中国北京市海淀区中关村大街1号</li>
                        </ul>
                        
                        <h5 class="mt-4">关注我们</h5>
                        <div class="social-icons">
                            <a href="#" class="text-decoration-none me-3"><i class="fab fa-weixin fa-2x"></i></a>
                            <a href="#" class="text-decoration-none me-3"><i class="fab fa-weibo fa-2x"></i></a>
                            <a href="#" class="text-decoration-none me-3"><i class="fab fa-github fa-2x"></i></a>
                            <a href="#" class="text-decoration-none"><i class="fab fa-linkedin fa-2x"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}