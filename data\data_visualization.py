# -*- coding: utf-8 -*-
"""
数据可视化模块，用于生成电商数据的可视化图表
"""

import os
import logging
import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from wordcloud import WordCloud

from .data_analysis import get_analyzer
from .models import Product, Category

# 配置日志
logger = logging.getLogger(__name__)

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
except Exception as e:
    logger.warning(f"设置中文字体失败: {str(e)}")

class DataVisualizer:
    """
    数据可视化类，提供各种数据可视化功能
    """
    
    def __init__(self, output_dir=None):
        """
        初始化数据可视化器
        
        Args:
            output_dir: 输出目录，如果为None则使用默认目录
        """
        self.analyzer = get_analyzer()
        
        if output_dir:
            self.output_dir = output_dir
        else:
            # 默认输出目录
            self.output_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static/images/charts")
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_price_distribution_chart(self, category_name=None, save=True):
        """
        生成价格分布图
        
        Args:
            category_name: 分类名称，如果为None则获取所有分类
            save: 是否保存图表
            
        Returns:
            str: 图表文件路径
        """
        # 获取价格区间分布
        distribution = self.analyzer.get_price_range_distribution(category_name)
        
        if not distribution:
            logger.warning("未获取到价格分布数据")
            return None
        
        # 绘制柱状图
        plt.figure(figsize=(10, 6))
        bars = plt.bar(distribution.keys(), distribution.values(), color='#5DA5DA')
        
        # 添加数据标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{height}',
                    ha='center', va='bottom', fontsize=9)
        
        plt.title(f"价格分布 - {category_name if category_name else '所有分类'}")
        plt.xlabel("价格区间 (元)")
        plt.ylabel("产品数量")
        plt.xticks(rotation=45)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        if save:
            # 保存图片
            category_str = category_name.replace(' ', '_') if category_name else 'all'
            filename = f"price_distribution_{category_str}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"价格分布图已保存到 {filepath}")
            return filename
        else:
            return plt.gcf()
    
    def generate_price_trend_chart(self, category_name=None, days=30, save=True):
        """
        生成价格趋势图
        
        Args:
            category_name: 分类名称，如果为None则获取所有分类
            days: 天数
            save: 是否保存图表
            
        Returns:
            str: 图表文件路径
        """
        # 获取价格趋势
        trend = self.analyzer.get_price_trend(category_name=category_name, days=days)
        
        if not trend:
            logger.warning("未获取到价格趋势数据")
            return None
        
        # 转换为DataFrame
        df = pd.DataFrame(list(trend.items()), columns=['date', 'price'])
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        
        # 绘制折线图
        plt.figure(figsize=(12, 6))
        plt.plot(df['date'], df['price'], marker='o', linestyle='-', color='#5DA5DA', linewidth=2, markersize=6)
        
        # 添加数据标签
        for i, (date, price) in enumerate(zip(df['date'], df['price'])):
            if i % 3 == 0:  # 每隔3个点添加一个标签，避免拥挤
                # 将price转换为float类型再相加
                plt.text(date, float(price) + 0.5, f'{price}', ha='center', va='bottom', fontsize=8)
        
        plt.title(f"价格趋势 - {category_name if category_name else '所有分类'} (过去{days}天)")
        plt.xlabel("日期")
        plt.ylabel("平均价格 (元)")
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        if save:
            # 保存图片
            category_str = category_name.replace(' ', '_') if category_name else 'all'
            filename = f"price_trend_{category_str}_{days}days.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"价格趋势图已保存到 {filepath}")
            return filename
        else:
            return plt.gcf()
    
    def generate_category_stats_chart(self, save=True):
        """
        生成分类统计图
        
        Args:
            save: 是否保存图表
            
        Returns:
            tuple: (产品数量图表文件路径, 平均价格图表文件路径)
        """
        # 获取分类统计信息
        stats = self.analyzer.get_category_stats()
        
        if not stats:
            logger.warning("未获取到分类统计信息")
            return None, None
        
        # 转换为DataFrame
        df = pd.DataFrame(stats)
        
        # 按产品数量排序
        df = df.sort_values('product_count', ascending=False)
        
        # 绘制产品数量柱状图
        plt.figure(figsize=(12, 6))
        bars = plt.bar(df['name'], df['product_count'], color='#5DA5DA')
        
        # 添加数据标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{height}',
                    ha='center', va='bottom', fontsize=9)
        
        plt.title("各分类产品数量")
        plt.xlabel("分类")
        plt.ylabel("产品数量")
        plt.xticks(rotation=45)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        count_filename = None
        if save:
            # 保存图片
            count_filename = "category_product_count.png"
            filepath = os.path.join(self.output_dir, count_filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"分类产品数量图已保存到 {filepath}")
        
        # 绘制平均价格柱状图
        plt.figure(figsize=(12, 6))
        bars = plt.bar(df['name'], df['avg_price'], color='#F17CB0')
        
        # 添加数据标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    f'{height:.2f}',
                    ha='center', va='bottom', fontsize=9)
        
        plt.title("各分类平均价格")
        plt.xlabel("分类")
        plt.ylabel("平均价格 (元)")
        plt.xticks(rotation=45)
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        price_filename = None
        if save:
            # 保存图片
            price_filename = "category_avg_price.png"
            filepath = os.path.join(self.output_dir, price_filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"分类平均价格图已保存到 {filepath}")
            
            return count_filename, price_filename
        else:
            return plt.gcf()
    
    def generate_sales_trend_chart(self, category_name=None, days=30, save=True):
        """
        生成销量趋势图
        
        Args:
            category_name: 分类名称，如果为None则获取所有分类
            days: 天数
            save: 是否保存图表
            
        Returns:
            str: 图表文件路径
        """
        # 获取销量趋势
        trend = self.analyzer.get_sales_trend(category_name=category_name, days=days)
        
        if not trend:
            logger.warning("未获取到销量趋势数据")
            return None
        
        # 转换为DataFrame
        df = pd.DataFrame(list(trend.items()), columns=['date', 'sales'])
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        
        # 绘制折线图
        plt.figure(figsize=(12, 6))
        plt.plot(df['date'], df['sales'], marker='o', linestyle='-', color='#F17CB0', linewidth=2, markersize=6)
        
        # 添加数据标签
        for i, (date, sales) in enumerate(zip(df['date'], df['sales'])):
            if i % 3 == 0:  # 每隔3个点添加一个标签，避免拥挤
                # 将sales转换为float类型再相加
                plt.text(date, float(sales) + 0.5, f'{float(sales):.1f}', ha='center', va='bottom', fontsize=8)
        
        plt.title(f"销量趋势 - {category_name if category_name else '所有分类'} (过去{days}天)")
        plt.xlabel("日期")
        plt.ylabel("平均销量")
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        if save:
            # 保存图片
            category_str = category_name.replace(' ', '_') if category_name else 'all'
            filename = f"sales_trend_{category_str}_{days}days.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"销量趋势图已保存到 {filepath}")
            return filename
        else:
            return plt.gcf()
    
    def generate_shop_stats_chart(self, limit=10, save=True):
        """
        生成店铺统计图
        
        Args:
            limit: 返回的店铺数量
            save: 是否保存图表
            
        Returns:
            str: 图表文件路径
        """
        # 获取店铺统计信息
        stats = self.analyzer.get_shop_stats(limit=limit)
        
        if not stats:
            logger.warning("未获取到店铺统计信息")
            return None
        
        # 转换为DataFrame
        df = pd.DataFrame(stats)
        
        # 绘制水平条形图
        plt.figure(figsize=(12, 8))
        bars = plt.barh(df['name'], df['product_count'], color='#B2912F')
        
        # 添加数据标签
        for bar in bars:
            width = bar.get_width()
            plt.text(width + 0.1, bar.get_y() + bar.get_height()/2.,
                    f'{width}',
                    ha='left', va='center', fontsize=9)
        
        plt.title(f"热门店铺产品数量 (Top {limit})")
        plt.xlabel("产品数量")
        plt.ylabel("店铺名称")
        plt.grid(axis='x', linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        if save:
            # 保存图片
            filename = f"shop_stats_top{limit}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"店铺统计图已保存到 {filepath}")
            return filename
        else:
            return plt.gcf()
    
    def generate_location_stats_chart(self, limit=10, save=True):
        """
        生成地区统计图
        
        Args:
            limit: 返回的地区数量
            save: 是否保存图表
            
        Returns:
            str: 图表文件路径
        """
        # 获取地区统计信息
        stats = self.analyzer.get_location_stats()
        
        if not stats:
            logger.warning("未获取到地区统计信息")
            return None
        
        # 转换为DataFrame并限制数量
        df = pd.DataFrame(stats)[:limit]
        
        # 绘制饼图
        plt.figure(figsize=(10, 8))
        plt.pie(df['product_count'], labels=df['location'], autopct='%1.1f%%', 
                startangle=90, shadow=False, explode=[0.05] * len(df))
        plt.title(f"产品地区分布 (Top {limit})")
        plt.axis('equal')  # 保证饼图是圆的
        plt.tight_layout()
        
        if save:
            # 保存图片
            filename = f"location_stats_top{limit}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"地区统计图已保存到 {filepath}")
            return filename
        else:
            return plt.gcf()
    
    def generate_price_sales_scatter_chart(self, category_name=None, limit=100, save=True):
        """
        生成价格-销量散点图
        
        Args:
            category_name: 分类名称，如果为None则获取所有分类
            limit: 产品数量限制
            save: 是否保存图表
            
        Returns:
            str: 图表文件路径
        """
        # 获取产品数据
        session = self.analyzer.get_session()
        query = session.query(
            Product.price, Product.sales, Product.title
        ).filter(
            Product.price != None,
            Product.sales != None
        )
        
        # 添加分类过滤
        if category_name:
            query = query.join(Category).filter(Category.name == category_name)
        
        # 获取结果
        products = query.limit(limit).all()
        
        if not products:
            logger.warning("未获取到产品数据")
            return None
        
        # 提取数据
        prices = [p[0] for p in products]
        sales = [p[1] for p in products]
        titles = [p[2] for p in products]
        
        # 绘制散点图
        plt.figure(figsize=(12, 8))
        scatter = plt.scatter(prices, sales, c=prices, cmap='viridis', alpha=0.7, s=100)
        
        # 添加颜色条
        plt.colorbar(scatter, label='价格 (元)')
        
        # 添加趋势线
        z = np.polyfit(prices, sales, 1)
        p = np.poly1d(z)
        plt.plot(prices, p(prices), "r--", alpha=0.8, linewidth=2)
        
        plt.title(f"价格-销量关系 - {category_name if category_name else '所有分类'}")
        plt.xlabel("价格 (元)")
        plt.ylabel("销量")
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        
        if save:
            # 保存图片
            category_str = category_name.replace(' ', '_') if category_name else 'all'
            filename = f"price_sales_scatter_{category_str}.png"
            filepath = os.path.join(self.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"价格-销量散点图已保存到 {filepath}")
            return filename
        else:
            return plt.gcf()
    
    def generate_dashboard_charts(self, category_name=None, days=30):
        """
        生成仪表盘所需的所有图表
        
        Args:
            category_name: 分类名称，如果为None则获取所有分类
            days: 天数
            
        Returns:
            dict: 图表文件路径字典
        """
        charts = {}
        
        # 生成价格分布图
        charts['price_distribution'] = self.generate_price_distribution_chart(category_name)
        
        # 生成价格趋势图
        charts['price_trend'] = self.generate_price_trend_chart(category_name, days)
        
        # 生成销量趋势图
        charts['sales_trend'] = self.generate_sales_trend_chart(category_name, days)
        
        # 生成分类统计图
        count_chart, price_chart = self.generate_category_stats_chart()
        charts['category_count'] = count_chart
        charts['category_price'] = price_chart
        
        # 生成店铺统计图
        charts['shop_stats'] = self.generate_shop_stats_chart()
        
        # 生成地区统计图
        charts['location_stats'] = self.generate_location_stats_chart()
        
        # 生成价格-销量散点图
        charts['price_sales_scatter'] = self.generate_price_sales_scatter_chart(category_name)
        
        return charts

# 导出可视化器实例
def get_visualizer(output_dir=None):
    """
    获取数据可视化器实例
    
    Args:
        output_dir: 输出目录，如果为None则使用默认目录
        
    Returns:
        DataVisualizer: 数据可视化器实例
    """
    return DataVisualizer(output_dir)