# -*- coding: utf-8 -*-
"""
爬虫主程序，用于启动爬虫并保存数据
"""

import os
import json
import time
import logging
import argparse
from datetime import datetime

from .taobao_crawler import TaobaoCrawler
from .config import ELECTRONIC_CATEGORIES, PAGES_PER_KEYWORD, GET_DETAILS, MAX_PRODUCTS_PER_CATEGORY

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("crawler.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def save_to_json(data, filename):
    """
    将数据保存为JSON文件
    
    Args:
        data: 要保存的数据
        filename: 文件名
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"数据已保存到 {filename}")
    except Exception as e:
        logger.error(f"保存数据到 {filename} 失败: {str(e)}")


def main(categories=None, output_dir="../data/raw", test_mode=False):
    """
    爬虫主函数
    
    Args:
        categories: 要爬取的分类列表，如果为None则爬取所有分类
        output_dir: 输出目录
        test_mode: 是否为测试模式
    """
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 确定要爬取的分类
    if categories is None:
        categories_to_crawl = ELECTRONIC_CATEGORIES
    else:
        categories_to_crawl = [cat for cat in ELECTRONIC_CATEGORIES if cat["name"] in categories]
    
    # 如果是测试模式，则只爬取每个分类的第一个关键词的第一页
    if test_mode:
        logger.info("运行在测试模式，将只爬取每个分类的第一个关键词的第一页")
        pages_per_keyword = 1
        for cat in categories_to_crawl:
            cat["keywords"] = cat["keywords"][:1]
    else:
        pages_per_keyword = PAGES_PER_KEYWORD
    
    # 创建爬虫实例
    crawler = TaobaoCrawler()
    
    try:
        # 遍历分类
        for category in categories_to_crawl:
            category_name = category["name"]
            keywords = category["keywords"]
            
            logger.info(f"开始爬取分类: {category_name}")
            
            # 爬取该分类的所有关键词
            products = crawler.crawl(
                keywords=keywords,
                pages_per_keyword=pages_per_keyword,
                get_details=GET_DETAILS
            )
            
            # 限制每个分类的商品数量
            if len(products) > MAX_PRODUCTS_PER_CATEGORY:
                logger.info(f"分类 {category_name} 的商品数量超过限制，将截取前 {MAX_PRODUCTS_PER_CATEGORY} 个")
                products = products[:MAX_PRODUCTS_PER_CATEGORY]
            
            # 保存数据
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(output_dir, f"{category_name}_{timestamp}.json")
            save_to_json(products, filename)
            
            logger.info(f"分类 {category_name} 爬取完成，共获取 {len(products)} 个商品数据")
            
            # 分类间随机延迟
            if category != categories_to_crawl[-1]:  # 如果不是最后一个分类
                delay = 10 if test_mode else 30
                logger.info(f"等待 {delay} 秒后继续下一个分类")
                time.sleep(delay)
        
        logger.info("所有分类爬取完成")
        
    except KeyboardInterrupt:
        logger.info("用户中断爬取过程")
    except Exception as e:
        logger.error(f"爬取过程中发生错误: {str(e)}")
    finally:
        # 关闭爬虫
        crawler.close()


if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="淘宝电子产品爬虫")
    parser.add_argument("--categories", nargs="*", help="要爬取的分类列表，用空格分隔")
    parser.add_argument("--output", default="../data/raw", help="输出目录")
    parser.add_argument("--test", action="store_true", help="测试模式")
    
    args = parser.parse_args()
    
    main(categories=args.categories, output_dir=args.output, test_mode=args.test)
