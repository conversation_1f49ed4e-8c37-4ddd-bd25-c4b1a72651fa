import requests
from lxml import etree

headers = {
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/128.0.0.0 Safari/537.36 Edg/128.0.0.0"
}

response = requests.get("https://s.taobao.com/search?commend=all&ie=utf8&initiative_id=tbindexz_20170306&page=1&q=%E7%AC%94%E8%AE%B0%E6%9C%AC%E7%94%B5%E8%84%91&search_type=item&sourceId=tb.index&spm=a21bo.jianhua%2Fa.201867-main.d2_2_1.5af92a89vnzvht&ssid=s5-e&tab=all", headers=headers)

dom = etree.HTML(response.text)
dom.xpath()

