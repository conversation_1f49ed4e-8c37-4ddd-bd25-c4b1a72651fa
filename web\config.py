# -*- coding: utf-8 -*-
"""
Flask应用配置文件
"""

import os
from dotenv import load_dotenv

# 加载.env文件中的环境变量
load_dotenv()


class Config:
    """
    基础配置类
    """
    # Flask配置
    SECRET_KEY = os.environ.get('FLASK_SECRET_KEY', 'dev_key_for_taobao_analysis')
    DEBUG = False
    TESTING = False
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://{}:{}@{}:{}/{}'.format(
        os.environ.get('DB_USER', 'root'),
        os.environ.get('DB_PASSWORD', 'password'),
        os.environ.get('DB_HOST', 'localhost'),
        os.environ.get('DB_PORT', '3306'),
        os.environ.get('DB_NAME', 'taobao_electronics')
    )
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = False
    
    # 应用配置
    APP_NAME = "淘宝电子产品数据分析系统"
    CHARTS_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'output')
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'uploads')
    ALLOWED_EXTENSIONS = {'json'}
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 分页配置
    ITEMS_PER_PAGE = 20
    
    # 缓存配置
    CACHE_TYPE = "SimpleCache"
    CACHE_DEFAULT_TIMEOUT = 300  # 5分钟


class DevelopmentConfig(Config):
    """
    开发环境配置
    """
    DEBUG = True
    SQLALCHEMY_ECHO = True


class TestingConfig(Config):
    """
    测试环境配置
    """
    TESTING = True
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'


class ProductionConfig(Config):
    """
    生产环境配置
    """
    SECRET_KEY = os.environ.get('FLASK_SECRET_KEY')
    # 生产环境可以添加更多安全相关配置
    # 例如：SESSION_COOKIE_SECURE = True
    # REMEMBER_COOKIE_SECURE = True


# 配置字典，用于根据环境选择配置
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}