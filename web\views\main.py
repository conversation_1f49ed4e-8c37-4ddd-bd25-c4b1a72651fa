# -*- coding: utf-8 -*-
"""
主页视图模块，处理网站首页和基本导航
"""

from flask import Blueprint, render_template, current_app, redirect, url_for
from sqlalchemy import func

from web.app import db, cache
from data.models import Category, Product, Shop

# 创建蓝图
main_bp = Blueprint('main', __name__)


@main_bp.route('/')
@cache.cached(timeout=300)  # 缓存5分钟
def index():
    """
    网站首页
    显示基本统计信息和导航
    """
    # 获取基本统计信息
    stats = get_dashboard_stats()
    
    # 获取最近添加的产品
    recent_products = Product.query.order_by(Product.created_at.desc()).limit(5).all()
    
    return render_template(
        'main/index.html',
        stats=stats,
        recent_products=recent_products
    )


@main_bp.route('/about')
def about():
    """
    关于页面
    """
    return render_template('main/about.html')


@main_bp.route('/dashboard')
@cache.cached(timeout=300)  # 缓存5分钟
def dashboard():
    """
    数据仪表盘
    显示系统概览和关键指标
    """
    # 获取仪表盘统计信息
    stats = get_dashboard_stats()
    
    # 获取分类统计
    categories = Category.query.all()
    category_stats = []
    for category in categories:
        product_count = Product.query.filter_by(category_id=category.id).count()
        category_stats.append({
            'id': category.id,
            'name': category.name,
            'product_count': product_count
        })
    
    # 获取价格区间分布
    price_ranges = [
        {'min': 0, 'max': 100, 'label': '0-100元'},
        {'min': 100, 'max': 500, 'label': '100-500元'},
        {'min': 500, 'max': 1000, 'label': '500-1000元'},
        {'min': 1000, 'max': 3000, 'label': '1000-3000元'},
        {'min': 3000, 'max': 10000, 'label': '3000-10000元'},
        {'min': 10000, 'max': float('inf'), 'label': '10000元以上'}
    ]
    
    price_distribution = []
    for price_range in price_ranges:
        if price_range['max'] == float('inf'):
            count = Product.query.filter(Product.price >= price_range['min']).count()
        else:
            count = Product.query.filter(
                Product.price >= price_range['min'],
                Product.price < price_range['max']
            ).count()
        
        price_distribution.append({
            'label': price_range['label'],
            'count': count
        })
    
    return render_template(
        'main/dashboard.html',
        stats=stats,
        category_stats=category_stats,
        price_distribution=price_distribution
    )


def get_dashboard_stats():
    """
    获取仪表盘统计信息
    
    Returns:
        dict: 包含各种统计信息的字典
    """
    # 产品总数
    product_count = db.session.query(func.count(Product.id)).scalar()
    
    # 分类总数
    category_count = db.session.query(func.count(Category.id)).scalar()
    
    # 店铺总数
    shop_count = db.session.query(func.count(Shop.id)).scalar()
    
    # 平均价格
    avg_price = db.session.query(func.avg(Product.price)).scalar()
    if avg_price is None:
        avg_price = 0
    
    # 最高价格产品
    max_price_product = Product.query.order_by(Product.price.desc()).first()
    
    # 最低价格产品
    min_price_product = Product.query.filter(Product.price > 0).order_by(Product.price.asc()).first()
    
    # 销量最高的产品
    top_selling_product = Product.query.order_by(Product.sales.desc()).first()
    
    return {
        'product_count': product_count,
        'category_count': category_count,
        'shop_count': shop_count,
        'avg_price': round(avg_price, 2) if avg_price else 0,
        'max_price_product': max_price_product,
        'min_price_product': min_price_product,
        'top_selling_product': top_selling_product
    }