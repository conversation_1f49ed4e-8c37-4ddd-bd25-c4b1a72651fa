# -*- coding: utf-8 -*-
"""
产品视图模块，处理产品列表、详情和搜索功能
"""

from flask import Blueprint, render_template, request, current_app, abort, jsonify
from sqlalchemy import or_, func

from web.app import db, cache
from data.models import Product, Category, Shop, PriceHistory
from web.forms.product import ProductSearchForm

# 创建蓝图
products_bp = Blueprint('products', __name__)


@products_bp.route('/')
@products_bp.route('/list')
def product_list():
    """
    产品列表页面
    支持分页、排序和过滤
    """
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    sort_by = request.args.get('sort', 'created_at')
    order = request.args.get('order', 'desc')
    category_id = request.args.get('category', None, type=int)
    min_price = request.args.get('min_price', None, type=float)
    max_price = request.args.get('max_price', None, type=float)
    
    # 创建查询
    query = Product.query
    
    # 应用过滤条件
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    if min_price is not None:
        query = query.filter(Product.price >= min_price)
    
    if max_price is not None:
        query = query.filter(Product.price <= max_price)
    
    # 应用排序
    if hasattr(Product, sort_by):
        sort_attr = getattr(Product, sort_by)
        if order == 'desc':
            query = query.order_by(sort_attr.desc())
        else:
            query = query.order_by(sort_attr.asc())
    else:
        # 默认按创建时间降序排序
        query = query.order_by(Product.created_at.desc())
    
    # 执行分页查询
    products = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 获取所有分类，用于过滤
    categories = Category.query.all()
    
    # 创建搜索表单
    search_form = ProductSearchForm()
    
    return render_template(
        'products/list.html',
        products=products,
        categories=categories,
        current_category_id=category_id,
        min_price=min_price,
        max_price=max_price,
        sort_by=sort_by,
        order=order,
        search_form=search_form
    )


@products_bp.route('/<int:product_id>')
@cache.cached(timeout=300)  # 缓存5分钟
def product_detail(product_id):
    """
    产品详情页面
    
    Args:
        product_id: 产品ID
    """
    # 获取产品信息
    product = Product.query.get_or_404(product_id)
    
    # 获取店铺信息
    shop = Shop.query.get(product.shop_id) if product.shop_id else None
    
    # 获取价格历史
    price_history = PriceHistory.query.filter_by(product_id=product_id).order_by(PriceHistory.date).all()
    
    # 获取同类产品推荐
    similar_products = Product.query.filter(
        Product.category_id == product.category_id,
        Product.id != product.id
    ).order_by(func.random()).limit(4).all()
    
    return render_template(
        'products/detail.html',
        product=product,
        shop=shop,
        price_history=price_history,
        similar_products=similar_products
    )


@products_bp.route('/search')
def product_search():
    """
    产品搜索页面
    支持关键词搜索、分类过滤和价格范围过滤
    """
    # 获取查询参数
    keyword = request.args.get('keyword', '')
    category_id = request.args.get('category_id', None, type=int)
    min_price = request.args.get('min_price', None, type=float)
    max_price = request.args.get('max_price', None, type=float)
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config['ITEMS_PER_PAGE']
    
    # 创建查询
    query = Product.query
    
    # 应用关键词搜索
    if keyword:
        search_term = f'%{keyword}%'
        query = query.filter(
            or_(
                Product.title.like(search_term),
                Product.description.like(search_term)
            )
        )
    
    # 应用分类过滤
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    # 应用价格范围过滤
    if min_price is not None:
        query = query.filter(Product.price >= min_price)
    
    if max_price is not None:
        query = query.filter(Product.price <= max_price)
    
    # 执行分页查询
    products = query.order_by(Product.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 获取所有分类，用于过滤
    categories = Category.query.all()
    
    # 创建搜索表单并填充当前搜索条件
    search_form = ProductSearchForm(
        keyword=keyword,
        category_id=category_id,
        min_price=min_price,
        max_price=max_price
    )
    
    return render_template(
        'products/search.html',
        keyword=keyword,
        products=products,
        categories=categories,
        search_form=search_form
    )


@products_bp.route('/category/<int:category_id>')
def products_by_category(category_id):
    """
    按分类查看产品
    
    Args:
        category_id: 分类ID
    """
    # 获取分类信息
    category = Category.query.get_or_404(category_id)
    
    # 重定向到产品列表页面，带上分类过滤参数
    return redirect(url_for('products.product_list', category=category_id))


@products_bp.route('/api/search')
def api_product_search():
    """
    产品搜索API
    用于AJAX搜索请求
    """
    # 获取查询参数
    keyword = request.args.get('keyword', '')
    category_id = request.args.get('category_id', None, type=int)
    limit = request.args.get('limit', 10, type=int)
    
    # 创建查询
    query = Product.query
    
    # 应用关键词搜索
    if keyword:
        search_term = f'%{keyword}%'
        query = query.filter(
            or_(
                Product.title.like(search_term),
                Product.description.like(search_term)
            )
        )
    
    # 应用分类过滤
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    # 执行查询
    products = query.order_by(Product.created_at.desc()).limit(limit).all()
    
    # 构建响应数据
    results = [{
        'id': product.id,
        'title': product.title,
        'price': product.price,
        'image_url': product.image_url,
        'url': url_for('products.product_detail', product_id=product.id)
    } for product in products]
    
    return jsonify(results)