# -*- coding: utf-8 -*-
"""
数据库连接模块，用于管理MySQL数据库连接
"""

import os
import logging
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

# 获取数据库配置
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "3306")
DB_USER = os.getenv("DB_USER", "root")
DB_PASSWORD = os.getenv("DB_PASSWORD", "")
DB_NAME = os.getenv("DB_NAME", "taobao_electronics")

# 创建数据库URL
DATABASE_URL = f"mysql+mysqldb://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset=utf8mb4"

# 创建基类
Base = declarative_base()

class DatabaseManager:
    """
    数据库管理类，用于管理数据库连接和会话
    """
    
    def __init__(self):
        """
        初始化数据库管理器
        """
        self.engine = None
        self.Session = None
        self.session = None
        self.init_engine()
    
    def init_engine(self):
        """
        初始化数据库引擎
        """
        try:
            self.engine = create_engine(DATABASE_URL, pool_recycle=3600, echo=False)
            self.Session = sessionmaker(bind=self.engine)
            logger.info("数据库引擎初始化成功")
        except Exception as e:
            logger.error(f"数据库引擎初始化失败: {str(e)}")
            raise
    
    def create_tables(self):
        """
        创建所有表
        """
        try:
            Base.metadata.create_all(self.engine)
            logger.info("数据库表创建成功")
        except Exception as e:
            logger.error(f"数据库表创建失败: {str(e)}")
            raise
    
    def get_session(self):
        """
        获取数据库会话
        
        Returns:
            Session: 数据库会话
        """
        if not self.session:
            self.session = self.Session()
        return self.session
    
    def close_session(self):
        """
        关闭数据库会话
        """
        if self.session:
            self.session.close()
            self.session = None
            logger.debug("数据库会话已关闭")
    
    def __del__(self):
        """
        析构函数，确保会话被关闭
        """
        self.close_session()

# 创建数据库管理器实例
db_manager = DatabaseManager()

# 导出会话获取函数
def get_db_session():
    """
    获取数据库会话
    
    Returns:
        Session: 数据库会话
    """
    return db_manager.get_session()

# 导出关闭会话函数
def close_db_session():
    """
    关闭数据库会话
    """
    db_manager.close_session()