# 代码说明：
'''
代码功能： 基于ChromeDriver爬取taobao（淘宝）平台商品列表数据
输入参数:  KEYWORLD --> 搜索商品"关键词"；
          pageStart --> 爬取起始页；
          pageEnd --> 爬取终止页；
输出文件：爬取商品列表数据
        'Num'         ：序号
        'title'       ：商品标题
        'Price'       ：商品价格
        'Deal'        ：商品销量
        'Location'    ：地理位置
        'Shop'        ：商品
        'IsPostFree'  ：是否包邮
        'Title_URL'   ：商品详细页链接
        'Shop_URL'    ：商铺链接
        'Img_URL'     ：图片链接
        'shop_content'：店铺描述
'''
# 声明第三方库/头文件

from lxml import etree
from selenium import webdriver
from selenium.common.exceptions import TimeoutException, NoSuchElementException, StaleElementReferenceException
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from pyquery import PyQuery as pq
import time
import random  # 随机延迟
import csv  # 导入CSV库
import os  # 导入操作系统库

# 全局变量
count = 1  # 商品计数

KEYWORD = "笔记本电脑"  # 要搜索的商品的关键词
pageStart = 1 # 爬取起始页
pageEnd = 100 #  爬取终止页 假定为100页

# 启动ChromeDriver服务
options = webdriver.EdgeOptions()

# 关闭自动测试状态显示 // 会导致浏览器报：请停用开发者模式
options.add_experimental_option("excludeSwitches", ['enable-automation'])
# 把chrome设为selenium驱动的浏览器代理；
service = Service('./resource/msedgedriver.exe')
driver = webdriver.Edge(options=options, service=service)
# 反爬机制
driver.execute_cdp_cmd("Page.addScriptToEvaluateOnNewDocument", {"source": """Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"""})
# 窗口最大化
driver.maximize_window()
driver.get('https://s.taobao.com/')
# wait是Selenium中的一个等待类，用于在特定条件满足之前等待一定的时间(这里是20秒)。
# 如果一直到等待时间都没满足则会捕获TimeoutException异常
wait = WebDriverWait(driver, 20)

# 创建CSV文件
csv_filename = "{}_{}.csv".format(KEYWORD, "数据")
csv_file = open(csv_filename, 'a+', newline='', encoding='utf-8-sig')  # 使用utf-8-sig编码支持中文
csv_writer = csv.writer(csv_file)
# 写入CSV表头
title_list = ['序号', '商品标题', '价格', '销量', '地理位置', '店铺名称', '是否包邮', '商品链接',
              '店铺链接', '图片链接', '店铺描述', '商品功能', "分类"]
title_list = ['序号', '商品标题', '价格', '销量', '地理位置', '店铺名称', '是否包邮', '商品链接',
              '店铺链接', '图片链接', '店铺描述', '商品功能', "分类", "抓取时间", "评分", "评论数量", ]
csv_writer.writerow(title_list)
print(f"CSV文件 {csv_filename} 创建成功")


# 随机延迟函数，模拟人类操作
def random_sleep(min_seconds, max_seconds):
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)
    return delay


# 输入"关键词"，搜索
def search_goods():
    try:
        print("正在搜索: {}".format(KEYWORD))

        # 找到搜索"输入框"
        input_box = wait.until(EC.element_to_be_clickable((By.XPATH, '//*[@id="q"]')))

        # 找到"搜索"按钮
        submit = wait.until(EC.element_to_be_clickable((By.XPATH, '//*[@id="J_SearchForm"]/div/div[1]/button')))

        # 输入框写入"关键词KeyWord"
        input_box.clear()  # 先清空输入框

        # 模拟人类输入，逐字输入并随机停顿
        for char in KEYWORD:
            input_box.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))

        random_sleep(1, 2)  # 输入完成后稍等片刻

        # 点击"搜索"按键
        submit.click()
        time.sleep(5)
        # 等待搜索结果加载
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'div.content--CUnfXXxv')))

        # 检查是否有滑块验证
        try:
            slider = driver.find_element(By.ID, 'nc_1_n1z')
            if slider.is_displayed():
                print("检测到滑块验证，请手动完成验证后按Enter继续...")
                input()
        except NoSuchElementException:
            pass  # 没有滑块，继续执行

        print("搜索完成！")
    except Exception as exc:
        print("search_goods函数错误！Error：{}".format(exc))
        # 重试搜索
        print("5秒后重试搜索...")
        time.sleep(5)
        try:
            driver.refresh()
            search_goods()
        except:
            print("重试搜索失败，请手动操作后按Enter继续...")
            input()


# 翻页至第pageStar页
def turn_pageStart():
    try:
        print("正在翻转:第{}页".format(pageStart))
        time.sleep(5)
        # 确保页面已完全加载
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'div.content--CUnfXXxv')))

        # 滑动到页面底端
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        random_sleep(1, 2)

        # 尝试找到页码输入框
        retries = 0
        while retries < 3:
            try:
                # 找到输入"页面"的表单，输入"起始页"
                pageInput = wait.until(EC.presence_of_element_located(
                    (By.XPATH, '//div[@id="search-content-leftWrap"]//div//input[@aria-label="请输入跳转到第几页"]')))
                pageInput.clear()
                pageInput.send_keys(str(pageStart))
                time.sleep(2)
                # 找到页面跳转的"确定"按钮，并且点击
                admit = wait.until(EC.element_to_be_clickable(
                    (By.XPATH, '//div[@id="search-content-leftWrap"]//button[.//span[contains(text(), "确定")]]')))
                admit.click()

                # 等待页面加载完成
                wait.until(EC.text_to_be_present_in_element(
                    (By.XPATH, '//div[@id="search-content-leftWrap"]//div//span//em'), str(pageStart)))

                print("已翻至:第{}页".format(pageStart))
                return
            except (TimeoutException, StaleElementReferenceException):
                retries += 1
                driver.refresh()
                random_sleep(2, 4)
                print(f"重试翻页... ({retries}/3)")

        print("翻页失败，请检查页面结构是否变化")
    except Exception as exc:
        print("turn_pageStart函数错误！Error：{}".format(exc))


# 获取每一页的商品信息；
def get_goods(page):
    try:
        # 声明全局变量count和pageEnd
        global count, pageEnd
        
        # 等待页面元素加载完成
        print(f"等待第{page}页数据加载...")
        wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'div.content--CUnfXXxv')))
        
        # 模拟人类浏览行为，滚动页面以加载所有内容
        for scroll in range(5):  # 分5次滚动到底部
            driver.execute_script(f"window.scrollTo(0, {(scroll + 1) * 1000});")  # 使用固定像素值
            random_sleep(1, 2)
            
        # 获取html网页
        html = driver.page_source

        # y_num = etree.HTML(html).xpath('//*[@id="search-content-leftWrap"]/div[2]/div[4]/div/div/span[1]/text()')[0]
        # y_num = int(y_num.replace("/", ""))
        # pageEnd = y_num
        print(f"检测到总页数为: {pageEnd}")

        doc = pq(html)

        # 提取所有商品的共同父元素的类选择器
        items = list(doc('div.content--CUnfXXxv > div > div').items())
        print(f"在第{page}页找到{len(items)}个商品项")

        for item in items:
            if item.find('.title--RoseSo8H').text() == '大家都在搜':
                continue
            elif item.find('.headTitleText--hxVemljn').text() == '对本次搜索体验满意吗':
                continue
            else:
                try:
                    # 定位商品标题
                    title = item.find('.title--qJ7Xg_90 span').text()
                    if not title:  # 跳过没有标题的项
                        continue

                    # 定位价格
                    price_text = item.find('.innerPriceWrapper--aAJhHXD4').text()

                    try:
                        price_text = str(price_text).replace('\n', '').replace('\r', '')
                        price = float(price_text) if price_text else 0.0
                    except ValueError:
                        price = 0.0

                    # 定位交易量
                    deal_text = item.find('.realSales--XZJiepmt').text()
                    # 安全处理交易量，确保不会因为空字符串导致错误
                    if deal_text:
                        deal_int = str(deal_text)

                    # 定位所在地信息
                    location = item.find('.procity--wlcT2xH9 span').text()

                    # 定位店名
                    shop = item.find('.shopNameText--DmtlsDKm').text()
                    # 店名描述
                    shop_content = item.find('.shopTagText--wujObz6g').text()

                    # 定位包邮的位置
                    postText = item.find('.subIconWrapper--Vl8zAdQn').text()
                    postText = "包邮" if "包邮" in postText else "/"

                    # 定位商品url
                    t_url = item.find('.doubleCardWrapperAdapt--mEcC7olq')
                    t_url_str = t_url.attr('href') if t_url else ""
                    if t_url_str and isinstance(t_url_str, str) and not t_url_str.startswith('http'):
                        t_url_str = 'https:' + t_url_str

                    # 定位店名url
                    shop_url = item.find('.TextAndPic--grkZAtsC a')
                    shop_url_str = shop_url.attr('href') if shop_url else ""
                    if shop_url_str and isinstance(shop_url_str, str) and not shop_url_str.startswith('http'):
                        shop_url_str = 'https:' + shop_url_str

                    shop_xn = item.find('.text--eAiSCa_r').text()

                    # 定位商品图片url
                    img = item.find('.mainPicAdaptWrapper--V_ayd2hD img')

                    img_url_str = img.attr('src') if img else ""
                    if img_url_str and isinstance(img_url_str, str) and not img_url_str.startswith('http'):
                        img_url_str = 'https:' + img_url_str

                    # 构建商品信息字典
                    product = {
                        'Page': page,
                        'Num': count,
                        'title': title,
                        'price': price,
                        'deal': deal_int,
                        'location': location,
                        'shop': shop,
                        'isPostFree': postText,
                        'url': t_url_str,
                        'shop_url': shop_url_str,
                        'img_url': img_url_str,
                        'shop_content': shop_content,
                        'shop_xn': shop_xn,
                        "fl": KEYWORD

                    }
                    print(product)

                    # 将商品信息写入CSV文件
                    csv_writer.writerow([
                        count,  # 序号
                        title,  # 标题
                        price,  # 价格
                        deal_int,  # 付款人数
                        location,  # 地理位置
                        shop,  # 店铺名称
                        postText,  # 是否包邮
                        t_url_str,  # 商品链接
                        shop_url_str,  # 商铺链接
                        img_url_str,  # 图片链接
                        shop_content,  # 店铺描述
                        shop_xn,  # 店铺信誉
                        KEYWORD,  # 分类
                    ])

                    count += 1  # 下一行

                except Exception as item_exc:
                    print(f"处理商品时出错: {item_exc}")
                    continue

    except Exception as exc:
        print("get_goods函数错误！Error：{}".format(exc))


# 翻页函数
def page_turning(page_number):
    try:
        print("正在翻页: 第{}页".format(page_number))

        # 滚动到页面底部，确保翻页按钮可见
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        random_sleep(1, 2)

        # 尝试多次找到并点击"下一页"按钮
        retries = 0
        max_retries = 3

        while retries < max_retries:
            try:
                # 找到"下一页"的按钮
                next_button = wait.until(EC.element_to_be_clickable(
                    (By.XPATH, '//div[@id="search-content-leftWrap"]//div//button[contains(., "下一页")]')))
                next_button.click()

                # 等待新页面加载，确认页码
                wait.until(EC.text_to_be_present_in_element(
                    (By.XPATH, '//div[@id="search-content-leftWrap"]//div//span//em'), str(page_number)))

                # 等待商品加载完成
                wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'div.content--CUnfXXxv')))

                print("已翻至: 第{}页".format(page_number))
                return
            except (TimeoutException, StaleElementReferenceException) as e:
                retries += 1
                print(f"翻页遇到错误: {e}，重试 ({retries}/{max_retries})")
                random_sleep(2, 4)
                # 刷新页面再试
                if retries >= 2:
                    driver.refresh()
                    wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, 'div.content--CUnfXXxv')))

        raise Exception(f"翻页到第{page_number}页失败，已尝试{max_retries}次")

    except Exception as exc:
        print("page_turning函数错误！Error：{}".format(exc))

        # 如果翻页失败，尝试通过直接输入页码的方式跳转
        try:
            print(f"尝试通过页码输入跳转到第{page_number}页...")
            # 滑动到页面底端
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            random_sleep(1, 2)

            # 找到输入"页面"的表单，输入页码
            pageInput = wait.until(EC.presence_of_element_located(
                (By.XPATH, '//div[@id="search-content-leftWrap"]//div//input[@aria-label="页码输入框"]')))
            pageInput.clear()
            pageInput.send_keys(str(page_number))

            # 找到页面跳转的"确定"按钮，并且点击
            admit = wait.until(EC.element_to_be_clickable(
                (By.XPATH, '//div[@id="search-content-leftWrap"]//button[contains(text(), "确定")]')))
            admit.click()

            # 等待页面加载完成
            wait.until(EC.text_to_be_present_in_element(
                (By.XPATH, '//div[@id="search-content-leftWrap"]//div//span//em'), str(page_number)))

            print(f"已通过页码输入跳转到第{page_number}页")
        except Exception as e:
            print(f"通过页码输入跳转也失败了: {e}")
            print("跳过该页，继续下一页...")


# 爬虫main函数
def Crawer_main():
    try:
        # 搜索KEYWORD
        search_goods()

        # 判断pageStart是否为第1页
        if pageStart != 1:
            turn_pageStart()

        # 爬取PageStart的商品信息
        get_goods(pageStart)

        # 从PageStart+1爬取到PageEnd
        for i in range(pageStart + 1, pageEnd + 1):
            # 尝试翻页
            page_turning(i)

            # 爬取该页商品
            get_goods(i)

            # 每爬取10页，将CSV文件刷新到磁盘
            if i % 10 == 0:
                # 同时将CSV文件刷新到磁盘
                csv_file.flush()
                os.fsync(csv_file.fileno())
                print(f"已爬取{i}页，数据已保存到 {csv_filename}")

            # 随机延迟，避免被反爬
            random_sleep(3, 8)

    except Exception as exc:
        print("Crawer_main函数错误！Error：{}".format(exc))


if __name__ == '__main__':
    try:
        # 开始爬取数据
        Crawer_main()
    except KeyboardInterrupt:
        print("爬虫被手动中断")
    except Exception as e:
        print(f"爬虫运行出错: {e}")
    finally:
        # 关闭CSV文件
        if csv_file:
            csv_file.close()
            print(f"{csv_filename} 存储成功~")

        # 关闭浏览器
        # driver.quit()
        print("爬虫已结束，浏览器已关闭。")
