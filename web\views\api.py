# -*- coding: utf-8 -*-
"""
API视图模块，提供数据接口
"""

from flask import Blueprint, jsonify, request, current_app
from sqlalchemy import func, desc, or_
import pandas as pd

from web.app import db, cache
from data.models import Product, Category, Shop, PriceHistory, SalesHistory

# 创建蓝图
api_bp = Blueprint('api', __name__)


@api_bp.route('/categories')
def get_categories():
    """
    获取所有分类
    """
    categories = Category.query.all()
    result = [{
        'id': category.id,
        'name': category.name,
        'description': category.description,
        'product_count': Product.query.filter_by(category_id=category.id).count()
    } for category in categories]
    
    return jsonify(result)


@api_bp.route('/products')
def get_products():
    """
    获取产品列表
    支持分页、排序和过滤
    """
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', current_app.config['ITEMS_PER_PAGE'], type=int)
    sort_by = request.args.get('sort', 'created_at')
    order = request.args.get('order', 'desc')
    category_id = request.args.get('category_id', None, type=int)
    min_price = request.args.get('min_price', None, type=float)
    max_price = request.args.get('max_price', None, type=float)
    keyword = request.args.get('keyword', None)
    
    # 创建查询
    query = Product.query
    
    # 应用过滤条件
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    if min_price is not None:
        query = query.filter(Product.price >= min_price)
    
    if max_price is not None:
        query = query.filter(Product.price <= max_price)
    
    if keyword:
        search_term = f'%{keyword}%'
        query = query.filter(
            or_(
                Product.title.like(search_term),
                Product.description.like(search_term)
            )
        )
    
    # 应用排序
    if hasattr(Product, sort_by):
        sort_attr = getattr(Product, sort_by)
        if order == 'desc':
            query = query.order_by(sort_attr.desc())
        else:
            query = query.order_by(sort_attr.asc())
    else:
        # 默认按创建时间降序排序
        query = query.order_by(Product.created_at.desc())
    
    # 执行分页查询
    products_page = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 构建响应数据
    result = {
        'items': [{
            'id': product.id,
            'title': product.title,
            'price': product.price,
            'original_price': product.original_price,
            'sales': product.sales,
            'image_url': product.image_url,
            'url': product.url,
            'shop_id': product.shop_id,
            'category_id': product.category_id,
            'created_at': product.created_at.isoformat() if product.created_at else None,
            'updated_at': product.updated_at.isoformat() if product.updated_at else None
        } for product in products_page.items],
        'page': products_page.page,
        'per_page': products_page.per_page,
        'total': products_page.total,
        'pages': products_page.pages
    }
    
    return jsonify(result)


@api_bp.route('/products/<int:product_id>')
def get_product(product_id):
    """
    获取产品详情
    
    Args:
        product_id: 产品ID
    """
    product = Product.query.get_or_404(product_id)
    
    # 获取店铺信息
    shop = Shop.query.get(product.shop_id) if product.shop_id else None
    
    # 获取价格历史
    price_history = PriceHistory.query.filter_by(product_id=product_id).order_by(PriceHistory.date).all()
    price_history_data = [{
        'date': history.date.isoformat(),
        'price': history.price
    } for history in price_history]
    
    # 获取销量历史
    sales_history = SalesHistory.query.filter_by(product_id=product_id).order_by(SalesHistory.date).all()
    sales_history_data = [{
        'date': history.date.isoformat(),
        'sales': history.sales
    } for history in sales_history]
    
    # 构建响应数据
    result = {
        'id': product.id,
        'title': product.title,
        'description': product.description,
        'price': product.price,
        'original_price': product.original_price,
        'sales': product.sales,
        'image_url': product.image_url,
        'url': product.url,
        'shop': {
            'id': shop.id,
            'name': shop.name,
            'url': shop.url,
            'location': shop.location,
            'rating': shop.rating
        } if shop else None,
        'category_id': product.category_id,
        'category_name': Category.query.get(product.category_id).name if product.category_id else None,
        'created_at': product.created_at.isoformat() if product.created_at else None,
        'updated_at': product.updated_at.isoformat() if product.updated_at else None,
        'price_history': price_history_data,
        'sales_history': sales_history_data
    }
    
    return jsonify(result)


@api_bp.route('/shops')
def get_shops():
    """
    获取店铺列表
    支持分页、排序和过滤
    """
    # 获取查询参数
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', current_app.config['ITEMS_PER_PAGE'], type=int)
    sort_by = request.args.get('sort', 'rating')
    order = request.args.get('order', 'desc')
    location = request.args.get('location', None)
    min_rating = request.args.get('min_rating', None, type=float)
    
    # 创建查询
    query = Shop.query
    
    # 应用过滤条件
    if location:
        query = query.filter(Shop.location.like(f'%{location}%'))
    
    if min_rating is not None:
        query = query.filter(Shop.rating >= min_rating)
    
    # 应用排序
    if hasattr(Shop, sort_by):
        sort_attr = getattr(Shop, sort_by)
        if order == 'desc':
            query = query.order_by(sort_attr.desc())
        else:
            query = query.order_by(sort_attr.asc())
    else:
        # 默认按评分降序排序
        query = query.order_by(Shop.rating.desc())
    
    # 执行分页查询
    shops_page = query.paginate(page=page, per_page=per_page, error_out=False)
    
    # 构建响应数据
    result = {
        'items': [{
            'id': shop.id,
            'name': shop.name,
            'url': shop.url,
            'location': shop.location,
            'rating': shop.rating,
            'product_count': Product.query.filter_by(shop_id=shop.id).count()
        } for shop in shops_page.items],
        'page': shops_page.page,
        'per_page': shops_page.per_page,
        'total': shops_page.total,
        'pages': shops_page.pages
    }
    
    return jsonify(result)


@api_bp.route('/shops/<int:shop_id>')
def get_shop(shop_id):
    """
    获取店铺详情
    
    Args:
        shop_id: 店铺ID
    """
    shop = Shop.query.get_or_404(shop_id)
    
    # 获取店铺产品
    products = Product.query.filter_by(shop_id=shop_id).order_by(Product.created_at.desc()).all()
    products_data = [{
        'id': product.id,
        'title': product.title,
        'price': product.price,
        'sales': product.sales,
        'image_url': product.image_url,
        'category_id': product.category_id,
        'category_name': Category.query.get(product.category_id).name if product.category_id else None
    } for product in products]
    
    # 构建响应数据
    result = {
        'id': shop.id,
        'name': shop.name,
        'url': shop.url,
        'location': shop.location,
        'rating': shop.rating,
        'products': products_data,
        'product_count': len(products_data)
    }
    
    return jsonify(result)


@api_bp.route('/stats/basic')
@cache.cached(timeout=600)  # 缓存10分钟
def get_basic_stats():
    """
    获取基本统计信息
    """
    # 产品总数
    product_count = db.session.query(func.count(Product.id)).scalar()
    
    # 分类总数
    category_count = db.session.query(func.count(Category.id)).scalar()
    
    # 店铺总数
    shop_count = db.session.query(func.count(Shop.id)).scalar()
    
    # 平均价格
    avg_price = db.session.query(func.avg(Product.price)).scalar()
    if avg_price is None:
        avg_price = 0
    
    # 平均销量
    avg_sales = db.session.query(func.avg(Product.sales)).scalar()
    if avg_sales is None:
        avg_sales = 0
    
    # 价格区间分布
    price_ranges = [
        {'min': 0, 'max': 100, 'label': '0-100元'},
        {'min': 100, 'max': 500, 'label': '100-500元'},
        {'min': 500, 'max': 1000, 'label': '500-1000元'},
        {'min': 1000, 'max': 3000, 'label': '1000-3000元'},
        {'min': 3000, 'max': 10000, 'label': '3000-10000元'},
        {'min': 10000, 'max': float('inf'), 'label': '10000元以上'}
    ]
    
    price_distribution = []
    for price_range in price_ranges:
        if price_range['max'] == float('inf'):
            count = Product.query.filter(Product.price >= price_range['min']).count()
        else:
            count = Product.query.filter(
                Product.price >= price_range['min'],
                Product.price < price_range['max']
            ).count()
        
        price_distribution.append({
            'label': price_range['label'],
            'count': count
        })
    
    # 构建响应数据
    result = {
        'product_count': product_count,
        'category_count': category_count,
        'shop_count': shop_count,
        'avg_price': round(avg_price, 2),
        'avg_sales': round(avg_sales, 2),
        'price_distribution': price_distribution
    }
    
    return jsonify(result)


@api_bp.route('/stats/category/<int:category_id>')
@cache.cached(timeout=600)  # 缓存10分钟
def get_category_stats(category_id):
    """
    获取分类统计信息
    
    Args:
        category_id: 分类ID
    """
    # 获取分类信息
    category = Category.query.get_or_404(category_id)
    
    # 产品总数
    product_count = Product.query.filter_by(category_id=category_id).count()
    
    # 平均价格
    avg_price = db.session.query(func.avg(Product.price)).filter(Product.category_id == category_id).scalar()
    if avg_price is None:
        avg_price = 0
    
    # 平均销量
    avg_sales = db.session.query(func.avg(Product.sales)).filter(Product.category_id == category_id).scalar()
    if avg_sales is None:
        avg_sales = 0
    
    # 最高价格产品
    max_price_product = Product.query.filter_by(category_id=category_id).order_by(Product.price.desc()).first()
    
    # 最低价格产品
    min_price_product = Product.query.filter(Product.category_id == category_id, Product.price > 0).order_by(Product.price.asc()).first()
    
    # 销量最高的产品
    top_selling_product = Product.query.filter_by(category_id=category_id).order_by(Product.sales.desc()).first()
    
    # 构建响应数据
    result = {
        'id': category.id,
        'name': category.name,
        'description': category.description,
        'product_count': product_count,
        'avg_price': round(avg_price, 2),
        'avg_sales': round(avg_sales, 2),
        'max_price_product': {
            'id': max_price_product.id,
            'title': max_price_product.title,
            'price': max_price_product.price
        } if max_price_product else None,
        'min_price_product': {
            'id': min_price_product.id,
            'title': min_price_product.title,
            'price': min_price_product.price
        } if min_price_product else None,
        'top_selling_product': {
            'id': top_selling_product.id,
            'title': top_selling_product.title,
            'sales': top_selling_product.sales
        } if top_selling_product else None
    }
    
    return jsonify(result)