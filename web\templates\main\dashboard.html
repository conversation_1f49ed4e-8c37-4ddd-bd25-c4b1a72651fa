{% extends "layouts/base.html" %}

{% block title %}仪表盘 - 电商数据分析系统{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.min.css">
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title"><i class="fas fa-tachometer-alt me-2"></i>数据仪表盘</h2>
                <p class="text-muted">全面概览电商数据分析系统的关键指标和趋势</p>
                
                <!-- 筛选表单 -->
                <form method="get" action="{{ url_for('main.dashboard') }}" class="row g-3 mb-4">
                    <div class="col-md-4">
                        <label for="category" class="form-label">分类</label>
                        <select name="category" id="category" class="form-select">
                            <option value="">全部分类</option>
                            {% for cat in categories %}
                            <option value="{{ cat }}" {% if selected_category == cat %}selected{% endif %}>{{ cat }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="time_range" class="form-label">时间范围</label>
                        <select name="time_range" id="time_range" class="form-select">
                            <option value="7" {% if time_range == 7 %}selected{% endif %}>最近7天</option>
                            <option value="30" {% if time_range == 30 %}selected{% endif %}>最近30天</option>
                            <option value="90" {% if time_range == 90 %}selected{% endif %}>最近90天</option>
                            <option value="365" {% if time_range == 365 %}selected{% endif %}>最近一年</option>
                            <option value="all" {% if time_range == 'all' %}selected{% endif %}>全部时间</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">应用筛选</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 关键指标卡片 -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title">产品总数</h6>
                        <h2 class="mb-0">{{ stats.product_count }}</h2>
                    </div>
                    <div class="rounded-circle bg-white p-2">
                        <i class="fas fa-shopping-cart text-primary"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="{% if stats.product_growth > 0 %}text-success{% elif stats.product_growth < 0 %}text-danger{% else %}text-white{% endif %}">
                        <i class="fas {% if stats.product_growth > 0 %}fa-arrow-up{% elif stats.product_growth < 0 %}fa-arrow-down{% else %}fa-equals{% endif %} me-1"></i>
                        {{ stats.product_growth|abs }}%
                    </span>
                    <span class="ms-1">相比上期</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title">平均价格</h6>
                        <h2 class="mb-0">¥{{ stats.avg_price|round(2) }}</h2>
                    </div>
                    <div class="rounded-circle bg-white p-2">
                        <i class="fas fa-yuan-sign text-success"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="{% if stats.price_growth > 0 %}text-white{% elif stats.price_growth < 0 %}text-danger{% else %}text-white{% endif %}">
                        <i class="fas {% if stats.price_growth > 0 %}fa-arrow-up{% elif stats.price_growth < 0 %}fa-arrow-down{% else %}fa-equals{% endif %} me-1"></i>
                        {{ stats.price_growth|abs }}%
                    </span>
                    <span class="ms-1">相比上期</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title">平均销量</h6>
                        <h2 class="mb-0">{{ stats.avg_sales|round(0) }}</h2>
                    </div>
                    <div class="rounded-circle bg-white p-2">
                        <i class="fas fa-chart-line text-info"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="{% if stats.sales_growth > 0 %}text-white{% elif stats.sales_growth < 0 %}text-danger{% else %}text-white{% endif %}">
                        <i class="fas {% if stats.sales_growth > 0 %}fa-arrow-up{% elif stats.sales_growth < 0 %}fa-arrow-down{% else %}fa-equals{% endif %} me-1"></i>
                        {{ stats.sales_growth|abs }}%
                    </span>
                    <span class="ms-1">相比上期</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-dark h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title">店铺总数</h6>
                        <h2 class="mb-0">{{ stats.shop_count }}</h2>
                    </div>
                    <div class="rounded-circle bg-white p-2">
                        <i class="fas fa-store text-warning"></i>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="{% if stats.shop_growth > 0 %}text-success{% elif stats.shop_growth < 0 %}text-danger{% else %}text-dark{% endif %}">
                        <i class="fas {% if stats.shop_growth > 0 %}fa-arrow-up{% elif stats.shop_growth < 0 %}fa-arrow-down{% else %}fa-equals{% endif %} me-1"></i>
                        {{ stats.shop_growth|abs }}%
                    </span>
                    <span class="ms-1">相比上期</span>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- 销售趋势图 -->
    <div class="col-lg-8 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>销售趋势</h5>
            </div>
            <div class="card-body">
                <div id="sales-trend-chart" style="height: 350px;"></div>
            </div>
        </div>
    </div>

    <!-- 价格区间分布 -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>价格区间分布</h5>
            </div>
            <div class="card-body">
                <div id="price-distribution-chart" style="height: 350px;"></div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- 分类统计 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-tags me-2"></i>分类统计</h5>
            </div>
            <div class="card-body">
                <div id="category-chart" style="height: 350px;"></div>
            </div>
        </div>
    </div>

    <!-- 热门产品 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-fire me-2"></i>热门产品</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>产品名称</th>
                                <th>分类</th>
                                <th>价格</th>
                                <th>销量</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in top_products %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('products.product_detail', product_id=product.id) }}" class="text-decoration-none">
                                        {{ product.name }}
                                    </a>
                                </td>
                                <td>{{ product.category }}</td>
                                <td>¥{{ product.price }}</td>
                                <td>{{ product.sales_count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <!-- 评论情感分析 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-comments me-2"></i>评论情感分析</h5>
            </div>
            <div class="card-body">
                <div id="sentiment-chart" style="height: 350px;"></div>
            </div>
        </div>
    </div>

    <!-- 关键词云 -->
    <div class="col-lg-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-cloud me-2"></i>关键词云</h5>
            </div>
            <div class="card-body text-center">
                <img src="{{ url_for('static', filename='charts/wordcloud.png') }}" class="img-fluid" alt="关键词云" style="max-height: 350px;">
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.35.0/dist/apexcharts.min.js"></script>
<script>
    // 销售趋势图
    var salesTrendOptions = {
        series: [{
            name: '销量',
            data: {{ sales_trend.sales|tojson }}
        }, {
            name: '价格',
            data: {{ sales_trend.prices|tojson }}
        }],
        chart: {
            type: 'line',
            height: 350,
            toolbar: {
                show: true
            },
            zoom: {
                enabled: true
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: [3, 3]
        },
        colors: ['#0d6efd', '#20c997'],
        xaxis: {
            categories: {{ sales_trend.dates|tojson }},
            title: {
                text: '日期'
            }
        },
        yaxis: [{
            title: {
                text: '销量'
            },
        }, {
            opposite: true,
            title: {
                text: '价格 (¥)'
            }
        }],
        tooltip: {
            shared: true,
            intersect: false,
            y: {
                formatter: function (y, { seriesIndex }) {
                    if (seriesIndex === 0) {
                        return y.toFixed(0);
                    } else {
                        return '¥' + y.toFixed(2);
                    }
                }
            }
        },
        legend: {
            position: 'top'
        }
    };

    var salesTrendChart = new ApexCharts(document.querySelector("#sales-trend-chart"), salesTrendOptions);
    salesTrendChart.render();

    // 价格区间分布图
    var priceDistributionOptions = {
        series: {{ price_distribution.values|tojson }},
        chart: {
            type: 'donut',
            height: 350
        },
        labels: {{ price_distribution.labels|tojson }},
        colors: ['#0d6efd', '#6610f2', '#6f42c1', '#d63384', '#fd7e14'],
        legend: {
            position: 'bottom'
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 300
                },
                legend: {
                    position: 'bottom'
                }
            }
        }],
        tooltip: {
            y: {
                formatter: function(value) {
                    return value + ' 件产品';
                }
            }
        }
    };

    var priceDistributionChart = new ApexCharts(document.querySelector("#price-distribution-chart"), priceDistributionOptions);
    priceDistributionChart.render();

    // 分类统计图
    var categoryOptions = {
        series: [{
            name: '产品数量',
            data: {{ category_stats.counts|tojson }}
        }],
        chart: {
            type: 'bar',
            height: 350,
            toolbar: {
                show: true
            }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                columnWidth: '55%',
                endingShape: 'rounded'
            },
        },
        dataLabels: {
            enabled: false
        },
        colors: ['#0d6efd'],
        xaxis: {
            categories: {{ category_stats.categories|tojson }},
            title: {
                text: '分类'
            }
        },
        yaxis: {
            title: {
                text: '产品数量'
            }
        },
        fill: {
            opacity: 1
        },
        tooltip: {
            y: {
                formatter: function(val) {
                    return val + " 件产品"
                }
            }
        }
    };

    var categoryChart = new ApexCharts(document.querySelector("#category-chart"), categoryOptions);
    categoryChart.render();

    // 情感分析图
    var sentimentOptions = {
        series: {{ sentiment_data.values|tojson }},
        chart: {
            type: 'pie',
            height: 350
        },
        labels: {{ sentiment_data.labels|tojson }},
        colors: ['#198754', '#0dcaf0', '#ffc107', '#fd7e14', '#dc3545'],
        legend: {
            position: 'bottom'
        },
        responsive: [{
            breakpoint: 480,
            options: {
                chart: {
                    width: 300
                },
                legend: {
                    position: 'bottom'
                }
            }
        }],
        tooltip: {
            y: {
                formatter: function(value) {
                    return value + ' 条评论';
                }
            }
        }
    };

    var sentimentChart = new ApexCharts(document.querySelector("#sentiment-chart"), sentimentOptions);
    sentimentChart.render();
</script>
{% endblock %}