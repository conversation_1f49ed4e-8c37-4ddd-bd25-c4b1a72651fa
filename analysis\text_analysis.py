# -*- coding: utf-8 -*-
"""
文本分析模块，用于分析产品标题、评论等文本数据
"""

import pandas as pd
import numpy as np
import logging
import jieba
import jieba.analyse
import re
from collections import Counter
from sqlalchemy import func

from data.db_connection import get_db_session, close_db_session
from data.models import Product, Category, RawData

# 配置日志
logger = logging.getLogger(__name__)

# 加载停用词
def load_stopwords(file_path=None):
    """
    加载停用词表
    
    Args:
        file_path: 停用词文件路径，如果为None则使用默认停用词
        
    Returns:
        set: 停用词集合
    """
    default_stopwords = {
        '的', '了', '和', '是', '就', '都', '而', '及', '与', '着',
        '或', '一个', '没有', '我们', '你们', '他们', '她们', '它们',
        '这个', '那个', '这些', '那些', '不', '很', '非常', '太',
        '但是', '但', '所以', '因为', '如果', '虽然', '于是', '只是',
        '只有', '还有', '还是', '也', '又', '并', '可以', '可能',
        '一些', '一样', '一直', '一定', '一般', '一种', '一种', '一下',
        '现在', '已经', '正在', '还', '再', '又', '这样', '那样',
        '以', '以及', '以前', '以后', '已', '已经', '已有', '有',
        '有些', '有的', '有点', '没', '没有', '每', '每个', '为',
        '为了', '为什么', '为何', '何', '何时', '何处', '何人', '何事',
        '所', '所以', '所有', '他', '他人', '他们', '她', '她们',
        '它', '它们', '自', '自己', '自家', '自身', '我', '我们',
        '你', '你们', '由', '由于', '由此', '这', '这个', '这些',
        '那', '那个', '那些', '能', '能够', '来', '来自', '来着',
        '到', '到了', '到着', '给', '给予', '被', '被动', '被动式',
        '在', '在于', '最', '最后', '最近', '最高', '最低', '就',
        '就是', '该', '该当', '该地', '该处', '该是', '说', '说明',
        '说道', '走', '走了', '走着', '跟', '跟着', '从', '从来',
        '从此', '从而', '用', '用于', '让', '让我', '讲', '讲述',
        '设', '设使', '设或', '设若', '设置', '诸', '诸位', '诸如',
        '谁', '谁人', '谁料', '谁知', '多', '多么', '多少', '多数',
        '如', '如何', '如其', '如果', '如此', '如若', '始', '始终',
        '孰', '孰料', '孰知', '宁', '宁可', '宁肯', '宁愿', '它',
        '它们', '对', '对于', '对待', '对方', '对比', '将', '将才',
        '将要', '将近', '小', '小儿', '少', '尔', '尔后', '尚',
        '尚且', '尤', '尤其', '就', '就是', '就要', '尽', '尽管',
        '居', '居然', '届', '届时', '岂', '岂但', '岂止', '岂非',
        '川', '州', '左', '左右', '巨', '巨大', '巨细', '己',
        '已', '已矣', '已经', '巴', '巴巴', '巴拉', '布', '希',
        '帮', '帮助', '常', '常常', '常言', '常言道', '常见', '常识',
        '干', '干什么', '干吗', '干嘛', '平', '年', '年代', '年年',
        '并', '并且', '并排', '并无', '并没', '并没有', '并肩', '并非',
        '广', '广大', '广泛', '广阔', '应', '应当', '应该', '应该当',
        '去', '去了', '去哪儿', '去处', '又', '又及', '及', '及其',
        '及至', '反', '反之', '反倒', '反倒是', '反应', '反手', '反映',
        '反而', '反过来', '发', '取', '受', '变', '古', '古代',
        '古来', '另', '另一方面', '另外', '另悉', '只', '只当', '只怕',
        '只是', '只有', '只消', '只要', '只限', '叫', '叫做', '召',
        '召开', '叮', '叮咚', '可', '可以', '可是', '可能', '可见',
        '各', '各个', '各人', '各位', '各地', '各式', '各种', '各自',
        '合', '合乎', '合适', '合理', '同', '同一', '同时', '同样',
        '后', '后来', '后者', '向', '向使', '向着', '吓', '吓一跳',
        '吗', '否', '否则', '否则的话', '吧', '吧哒', '吱', '呀',
        '呃', '呆呆地', '呐', '呕', '呗', '呜', '呜呼', '呢',
        '周', '周围', '周详', '呵', '呵呵', '呸', '呼', '呼哧',
        '呼啦', '咋', '和', '咚', '咦', '咧', '咱', '咱们',
        '咳', '哇', '哈', '哈哈', '哉', '哎', '哎呀', '哎哟',
        '哗', '哗啦', '哟', '哦', '哩', '哪', '哪个', '哪些',
        '哪儿', '哪天', '哪年', '哪怕', '哪样', '哪边', '哪里', '哼',
        '哼唷', '唉', '唯', '唯有', '啊', '啊呀', '啊哈', '啊哟',
        '啐', '啥', '啦', '啪', '啪达', '啷', '喂', '喏',
        '喔', '喽', '嗡', '嗡嗡', '嗬', '嗯', '嗳', '嘎',
        '嘎嘎', '嘎登', '嘘', '嘛', '嘻', '嘿', '嘿嘿', '四',
        '因', '因为', '因了', '因此', '因着', '因而', '固', '固然',
        '在', '在下', '在于', '地', '地上', '地下', '地方', '地点',
        '地球', '地面', '地，', '均', '坚', '坚决', '坚持', '坚持不懈',
        '坚称', '坚韧不拔', '垂', '垂直', '垒', '型', '型号', '型式',
        '埋', '埋伏', '埋头', '城', '城市', '城镇', '域', '域名',
        '域外', '基', '基于', '基本', '基础', '堆', '堆积', '塔',
        '塔尖', '塔座', '塞', '填', '填写', '填充', '填补', '填满',
        '境', '境内', '境外', '墙', '墙上', '墙体', '墙壁', '墙外',
        '声', '声音', '壁', '壁上', '壁垒', '壁炉', '处', '处于',
        '处处', '处理', '复', '复印', '复合', '复杂', '复查', '复核',
        '外', '外侧', '外出', '外加', '外卖', '外围', '外国', '外地',
        '外型', '外头', '外套', '外层', '外延', '外形', '外援', '外文',
        '外族', '外来', '外界', '外省', '外科', '外籍', '外行', '外表',
        '外观', '外语', '外资', '外边', '外部', '外面', '外面儿', '多',
        '多么', '多亏', '多余', '多加', '多半', '多多', '多多少少', '多多益善',
        '多大', '多大年纪', '多好', '多少', '多少岁', '多年', '多年来', '多年生',
        '多得', '多数', '多方', '多早晚', '多时', '多样', '多次', '多次元',
        '多见', '多谢', '多远', '多长', '多长时间', '多高', '夜', '夜晚',
        '夜间', '够', '够了', '够瞧的', '够用', '大', '大不了', '大举',
        '大事', '大体', '大体上', '大凡', '大力', '大势', '大半', '大多',
        '大多数', '大大', '大大小小', '大家', '大小', '大张旗鼓', '大批', '大抵',
        '大概', '大略', '大约', '大致', '大都', '大量', '大面儿上', '天',
        '天地', '天天', '天子', '天时', '天气', '天然', '天生', '天空',
        '天花板', '天要', '太', '太多', '太好了', '太小', '太少', '太差',
        '太早', '太晚', '太棒了', '太阳', '太难', '头', '头上', '头发',
        '头部', '头里', '奇', '奇妙', '奇怪', '奇特', '奇迹', '奈',
        '奋', '奋力', '奋勇', '她', '她们', '她是', '她的', '好',
        '好了', '好像', '好在', '好多', '好处', '好好', '好感', '好戏',
        '好比', '好看', '好象', '如', '如上', '如上所述', '如下', '如今',
        '如何', '如其', '如前所述', '如同', '如常', '如是', '如期', '如果',
        '如次', '如此', '如此等等', '如若', '始', '始终', '姑', '姑且',
        '存', '存在', '孰', '孰知', '宁', '宁可', '宁愿', '宁肯',
        '它', '它们', '它们的', '它是', '它的', '安', '安全', '安全性',
        '安好', '安定', '安宁', '安心', '安排', '安稳', '安装', '安设',
        '安静', '宽', '宽厚', '宽大', '宽容', '宽宽', '宽广', '宽敞',
        '宽松', '宽泛', '宽窄', '宽裕', '宽阔', '宽限', '实', '实在',
        '实地', '实实在在', '实惠', '实际', '实际上', '实现', '实用', '实行',
        '实际', '实际上', '实际情况', '实验', '实验室', '实践', '实质', '实质上',
        '实际', '实际上', '实际情况', '实验', '实验室', '实践', '实质', '实质上',
        '宣', '宣传', '宣告', '宣布', '宣扬', '宣称', '宣讲', '宣读',
        '宣言', '宣誓', '宫', '宫中', '宫内', '宫外', '宫廷', '宫殿',
        '宫灯', '宫墙', '宫女', '宫中', '宫内', '宫外', '宫廷', '宫殿',
        '宫灯', '宫墙', '宫女', '家', '家中', '家人', '家伙', '家具',
        '家务', '家家', '家庭', '家族', '家用', '家电', '家眷', '家祖',
        '家谱', '家里', '家门', '家长', '容', '容不得', '容器', '容易',
        '容得', '容忍', '容纳', '容许', '宽', '宽厚', '宽大', '宽容',
        '宽宽', '宽广', '宽敞', '宽松', '宽泛', '宽窄', '宽裕', '宽阔',
        '宽限', '宾', '宾主', '宾客', '宾朋', '宾语', '宾馆', '宿',
        '宿命', '宿舍', '宿营', '密', '密切', '密友', '密封', '密度',
        '密探', '密林', '密码', '密集', '对', '对于', '对付', '对头',
        '对应', '对待', '对手', '对方', '对比', '对照', '对的', '对着',
        '对象', '对话', '对质', '对面', '寻', '寻找', '寻思', '寻求',
        '寻觅', '导', '导入', '导出', '导向', '导引', '导致', '导航',
        '导论', '导语', '小', '小于', '小人', '小伙子', '小伙计', '小区',
        '小吃', '小型', '小声', '小处', '小子', '小孩', '小家伙', '小小',
        '小巧', '小年', '小店', '小心', '小心翼翼', '小怪', '小手', '小数',
        '小时', '小朋友', '小样', '小步', '小气', '小河', '小溪', '小灶',
        '小猫', '小王', '小看', '小节', '小编', '小组', '小罐', '小而',
        '小聪明', '小腿', '小菜', '小虫', '小虾', '小虾米', '小袋', '小规模',
        '小说', '小贩', '小车', '小道', '小道消息', '小雨', '小鬼', '小鸟',
        '少', '少不了', '少于', '少儿', '少先队', '少女', '少年', '少数',
        '少时', '少许', '少量', '尔', '尔后', '尔尔', '尔等', '尚',
        '尚且', '尚未', '尚有', '尤', '尤为', '尤以', '尤其', '就',
        '就业', '就任', '就地', '就好', '就是', '就是了', '就是说', '就此',
        '就算', '就要', '就连', '就餐', '尺', '尺子', '尺寸', '尺度',
        '尺码', '尼', '尼姑', '尼龙', '尽', '尽可能', '尽快', '尽早',
        '尽然', '尽管', '尽管如此', '尽量', '尾', '尾巴', '尾数', '尾椎',
        '尾灯', '尾随', '尿', '尿尿', '尿布', '尿液', '尿裤', '局',
        '局促', '局势', '局域网', '局部', '局限', '局面', '层', '层出不穷',
        '层层', '层次', '居', '居中', '居于', '居住', '居士', '居多',
        '居家', '居心', '居所', '居然', '居高临下', '屈', '屈从', '屈服',
        '屈膝', '屋', '屋内', '屋外', '屋子', '屋檐', '屋里', '屏',
        '屏住', '屏幕', '屏息', '屏气', '屏蔽', '展', '展出', '展品',
        '展开', '展望', '展现', '展示', '展览', '展露', '属', '属于',
        '属下', '属地', '属性', '属意', '属意于', '属相', '属种', '属类',
        '山', '山上', '山下', '山丘', '山中', '山区', '山地', '山坡',
        '山头', '山寨', '山峦', '山峰', '山川', '山村', '山林', '山水',
        '山沟', '山洞', '山石', '山脉', '山脚', '山腰', '山路', '山里',
        '山顶', '山风', '岁', '岁数', '岁月', '岂', '岂不', '岂但',
        '岂止', '岂非', '岗', '岗位', '岗哨', '岗楼', '岗亭', '岛',
        '岛上', '岛国', '岛屿', '岛民', '岩', '岩层', '岩浆', '岩石',
        '岭', '岭上', '岭南', '岸', '岸上', '岸边', '峡', '峡口',
        '峡谷', '峰', '峰会', '峰值', '峰峦', '峰巅', '峰顶', '崇',
        '崇山峻岭', '崇尚', '崇拜', '崇敬', '崇高', '崖', '崖壁', '崖顶',
        '崭', '崭新', '川', '川流不息', '川菜', '州', '州县', '州官',
        '州市', '州立', '州长', '州际', '巡', '巡回', '巡察', '巡山',
        '巡查', '巡检', '巡游', '巡演', '巡视', '巡逻', '工', '工业',
        '工人', '工会', '工伤', '工作', '工具', '工厂', '工地', '工夫',
        '工序', '工整', '工期', '工程', '工程师', '工艺', '工艺品', '工薪',
        '工资', '工钱', '工龄', '左', '左侧', '左右', '左右手', '左手',
        '左方', '左派', '左眼', '左翼', '左边', '左面', '巧', '巧克力',
        '巧妙', '巧思', '巧手', '巧用', '巧舌如簧', '巧言令色', '巧遇', '巨',
        '巨人', '巨作', '巨兽', '巨变', '巨大', '巨头', '巨星', '巨款',
        '巨浪', '巨石', '巨细', '巨著', '巨额', '巨龙', '巩', '巩固',
        '巫', '巫师', '巫术', '差', '差一点', '差不多', '差别', '差劲',
        '差异', '差点', '差生', '差距', '差错', '己', '己任', '己见',
        '已', '已于', '已经', '已而', '巴', '巴不得', '巴士', '巴巴',
        '巴掌', '巴望', '巴结', '巴西', '巴金', '巷', '巷口', '巷子',
        '巷尾', '巷战', '巾', '巾帼', '币', '币值', '币制', '币种',
        '币种', '市', '市中心', '市井', '市价', '市内', '市区', '市场',
        '市委', '市容', '市政', '市政府', '市民', '市长', '市镇', '布',
        '布丁', '布下', '布告', '布局', '布满', '布置', '布艺', '布衣',
        '布袋', '布谷鸟', '布防', '布阵', '布雷', '布鞋', '帅', '帅哥',
        '帅气', '师', '师傅', '师兄', '师友', '师叔', '师大', '师太',
        '师奶', '师姐', '师姑', '师弟', '师徒', '师母', '师父', '师爷',
        '师生', '师范', '师范大学', '师范学校', '师表', '师资', '师长', '希',
        '希冀', '希图', '希奇', '希望', '希罕', '希腊', '帐', '帐单',
        '帐号', '帐户', '帐篷', '帐蓬', '帐薄', '帐面', '帕', '帕克',
        '帖', '帖子', '帘', '帘子', '帜', '帜旗', '帝', '帝国',
        '帝王', '带', '带上', '带下', '带人', '带入', '带出', '带动',
        '带去', '带回', '带头', '带子', '带宽', '带来', '带着', '带给',
        '带走', '带路', '带进', '带领', '帧', '帧数', '帮', '帮主',
        '帮人', '帮会', '帮凶', '帮助', '帮工', '帮忙', '帮手', '帮派',
        '帮腔', '帮衬', '帮闲', '常', '常事', '常人', '常会', '常住',
        '常例', '常务', '常去', '常在', '常备', '常客', '常年', '常常',
        '常态', '常情', '常数', '常日', '常有', '常来', '常用', '常理',
        '常用', '常用语', '常用词', '常礼', '常绿', '常胜', '常规', '常见',
        '常识', '常设', '常谈', '常识', '常设', '常谈', '常驻', '帽',
        '帽子', '幅', '幅员', '幅度', '幅面', '幌', '幌子', '幔',
        '幔子', '幕', '幕僚', '幕后', '幕墙', '幕布', '幕府', '幕间',
        '幢', '幢楼', '干', '干事', '干什么', '干净', '干劲', '干吗',
        '干嘛', '干扰', '干杯', '干枯', '干活', '干流', '干涉', '干涸',
        '干渠', '干燥', '干爹', '干瘪', '干的', '干眼', '干粉', '干粮',
        '干线', '干练', '干脆', '干草', '干菜', '干警', '干货', '干部',
        '干预', '平', '平伏', '平凡', '平分', '平台', '平和', '平地',
        '平均', '平坐', '平坦', '平安', '平定', '平常', '平平', '平平常常',
        '平庸', '平息', '平手', '平方', '平日', '平时', '平板', '平民',
        '平淡', '平添', '平滑', '平潭', '平生', '平白', '平直', '平等',
        '平米', '平素', '平缓', '平行', '平衡', '平装', '平角', '平话',
        '平调', '平路', '平躺', '平辈', '平远', '平铺', '平静', '平面',
        '平顶', '平顺', '年', '年中', '年事', '年代', '年份', '年会',
        '年假', '年关', '年初', '年前', '年华', '年历', '年夜', '年头',
        '年审', '年少', '年岁', '年年', '年幼', '年度', '年底', '年息',
        '年报', '年时', '年月', '年末', '年来', '年检', '年段', '年满',
        '年画', '年级', '年纪', '年终', '年老', '年节', '年薪', '年表',
        '年谱', '年货', '年费', '年轻', '年轮', '年迈', '年金', '年长',
        '年间', '年青', '年限', '年青', '年限', '年龄', '并', '并不',
        '并且', '并入', '并列', '并力', '并发', '并口', '并在', '并存',
        '并州', '并拢', '并排', '并未', '并条', '并没', '并没有', '并用',
        '并立', '并肩', '并行', '并购', '并进', '并重', '并非', '幸',
        '幸亏', '幸好', '幸存', '幸福', '幸而', '幸运', '幻', '幻影',
        '幻想', '幻术', '幻灯', '幻觉', '幼', '幼儿', '幼儿园', '幼小',
        '幼年', '幼稚', '幼童', '幼虫', '幽', '幽兰', '幽咽', '幽寂',
        '幽幽', '幽径', '幽暗', '幽灵', '幽默', '幽静', '幽香', '广',
        '广东', '广为', '广义', '广交', '广告', '广场', '广大', '广州',
        '广播', '广泛', '广漠', '广西', '广阔', '庄', '庄严', '庄园',
        '庄子', '庄家', '庄户', '庄稼', '庄稼人', '庄重', '庆', '庆典',
        '庆功', '庆幸', '庆祝', '庆贺', '床', '床上', '床位', '床单',
        '床垫', '床头', '床底', '床榻', '床沿', '床罩', '床铺', '床面',
        '序', '序列', '序号', '序幕', '序曲', '序言', '序论', '庐',
        '庐山', '庐舍', '库', '库仑', '库区', '库存', '库房', '库藏',
        '应', '应付', '应允', '应到', '应力', '应变', '应对', '应届',
        '应当', '应征', '应急', '应战', '应接不暇', '应收', '应时', '应有',
        '应机立断', '应用', '应答', '应考', '应聘', '应该', '应诺', '应邀',
        '应酬', '应门', '底', '底下', '底价', '底册', '底子', '底层',
        '底座', '底数', '底板', '底气', '底片', '底版', '底牌', '底盘',
        '底端', '底线', '底细', '底色', '底蕴', '底部', '底限', '底面',
        '底鞋', '庙', '庙会', '庙堂', '庙宇', '庙门', '庚', '庚子',
        '府', '府上', '府中', '府内', '府城', '府库', '府第', '府衙',
        '庞', '庞大', '庞杂', '庞然大物', '废', '废人', '废品', '废墟',
        '废寝忘食', '废弃', '废料', '废旧', '废水', '废气', '废物', '废纸',
        '废话', '废除', '度', '度假', '度假村', '度数', '度过', '座',
        '座位', '座右铭', '座垫', '座谈', '座谈会', '座钟', '座驾', '庭',
        '庭中', '庭前', '庭园', '庭外', '庭审', '庭长', '庭院', '庵',
        '庵堂', '庶', '庶几', '庶出', '庶民', '康', '康乃馨', '康健',
        '康复', '康宁', '康庄', '康泰', '康熙', '康王', '康乐', '廉',
        '廉价', '廉政', '廉明', '廉洁', '廉租房', '廉耻', '廉颇', '廊',
        '廊坊', '廊子', '廊桥', '廓', '廓清', '延', '延伸', '延后',
        '延安', '延展', '延年', '延年益寿', '延庆', '延期', '延续', '延缓',
        '延误', '延迟', '延长', '延髓', '廷', '廷臣', '建', '建交',
        '建仓', '建伟', '建党', '建军', '建制', '建功', '建华', '建厂',
        '建国', '建好', '建安', '建委', '建宁', '建安', '建委', '建宁',
        '建州', '建康', '建成', '建房', '建新', '建构', '建树', '建档',
        '建桥', '建模', '建水', '建没', '建立', '建筑', '建筑学', '建筑师',
        '建筑物', '建设', '建议', '建造', '建邺', '建都', '建阳', '开',
        '开业', '开个', '开交', '开仗', '开价', '开会', '开伙', '开元',
        '开光', '开关', '开具', '开出', '开刀', '开列', '开创', '开初',
        '开办', '开动', '开化', '开卖', '开卷', '开原', '开口', '开台',
        '开司', '开启', '开吊', '开合', '开吗', '开启', '开吊', '开合',
        '开吗', '开唱', '开国', '开场', '开垦', '开城', '开埠', '开垦',
        '开城', '开埠', '开堂', '开场', '开垦', '开城', '开埠', '开堂',
        '开场', '开垦', '开城', '开埠', '开堂', '开塞', '开士米', '开外',
        '开夜车', '开大', '开天', '开天辟地', '开头', '开始', '开学', '开导',
        '开封', '开导', '开封', '开小差', '开展', '开山', '开市', '开幕',
        '开年', '开庭', '开张', '开往', '开心', '开怀', '开恩', '开悟',
        '开戏', '开战', '开户', '开房', '开打', '开拍', '开拓', '开挖',
        '开掘', '开支', '开放', '开文', '开新', '开方', '开明', '开映',
        '开春', '开显', '开普勒', '开朗', '开机', '开来', '开枪', '开架',
        '开标', '开水', '开江', '开沟', '开河', '开泰', '开洋', '开涮',
        '开源', '开溜', '开满', '开漳', '开火', '开炮', '开片', '开玩笑',
        '开球', '开瓶', '开瓶器', '开盘', '开眼', '开矿', '开示', '开禁',
        '开福', '开窍', '开窗', '开立', '开端', '开笔', '开篇', '开红',
        '开绿灯', '开罗', '开罪', '开胃', '开胃酒', '开脱', '开腔', '开膛',
        '开航', '开船', '开花', '开苞', '开荒', '开荤', '开裂', '开襟',
        '开解', '开言', '开设', '开证', '开诚', '开诚布公', '开赛', '开赴',
        '开走', '开赴', '开走', '开足', '开路', '开车', '开辟', '开过',
        '开迈', '开运', '开进', '开远', '开通', '开释', '开采', '开释',
        '开采', '开释', '开采', '开门', '开闭', '开间', '开阔', '开除',
        '开颜', '开题', '开颅', '开饭', '开馆', '异', '异乎寻常', '异乡',
        '异交', '异人', '异位', '异体', '异军突起', '异化', '异口同声', '异同',
        '异味', '异国', '异地', '异域', '异姓', '异己', '异常', '异形',
        '异彩纷呈', '异性', '异教', '异族', '异曲同工', '异样', '异步', '异母',
        '异源', '异物', '异状', '异界', '异相', '异种', '异端', '异类',
        '异议', '异说', '异趣', '异香', '弃', '弃世', '弃之', '弃儿',
        '弃养', '弃城', '弃妇', '弃婴', '弃守', '弃官', '弃尸', '弃权',
        '弃绝', '弃置', '弄', '弄不好', '弄不懂', '弄乱', '弄假', '弄假成真',
        '弄出', '弄到', '弄坏', '弄堂', '弄好', '弄完', '弄巧成拙', '弄平',
        '弄得', '弄懂', '弄成', '弄斧', '弄斧班门', '弄明白', '弄权', '弄死',
        '弄清', '弄清楚', '弄潮', '弄璋', '弄瓦', '弄皱', '弄碎', '弄脏',
        '弄虚作假', '弄通', '弄错', '弄鬼', '弄鬼掉猴', '弊', '弊病', '弊端',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病'
    }
    
    if file_path:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                stopwords = set(line.strip() for line in f)
            return stopwords
        except Exception as e:
            logger.error(f"加载停用词文件失败: {e}")
            return default_stopwords
    else:
        return default_stopwords


class TextAnalyzer:
    """
    文本分析类，用于分析产品标题、评论等文本数据
    """
    
    def __init__(self, stopwords_path=None):
        """
        初始化文本分析器
        
        Args:
            stopwords_path: 停用词文件路径
        """
        self.stopwords = load_stopwords(stopwords_path)
        # 加载自定义词典
        self._load_custom_dict()
        
    def _load_custom_dict(self):
        """
        加载自定义词典，包括电子产品相关词汇
        """
        # 电子产品相关词汇
        electronic_terms = [
            "手机", "电脑", "笔记本", "平板", "电视", "冰箱", "洗衣机", 
            "空调", "相机", "耳机", "音箱", "充电器", "移动电源", "路由器", 
            "智能手表", "智能手环", "VR眼镜", "游戏机", "无人机", "摄像头",
            "iPhone", "iPad", "MacBook", "ThinkPad", "Surface", "Galaxy",
            "华为", "小米", "OPPO", "vivo", "荣耀", "魅族", "一加", "三星",
            "苹果", "联想", "戴尔", "惠普", "华硕", "宏碁", "微软", "索尼",
            "松下", "LG", "海尔", "美的", "格力", "TCL", "创维", "海信",
            "4K", "8K", "OLED", "QLED", "HDR", "5G", "4G", "WiFi", "蓝牙",
            "CPU", "GPU", "内存", "硬盘", "SSD", "HDD", "显卡", "主板",
            "散热器", "电源", "机箱", "显示器", "键盘", "鼠标", "音响",
            "麦克风", "摄像头", "打印机", "扫描仪", "投影仪", "智能家居",
            "智能音箱", "智能灯泡", "智能门锁", "智能插座", "智能窗帘",
            "智能马桶", "智能垃圾桶", "智能体重秤", "智能手表", "智能手环"
        ]
        
        # 品牌词汇
        brand_terms = [
            "华为", "小米", "OPPO", "vivo", "荣耀", "魅族", "一加", "三星",
            "苹果", "联想", "戴尔", "惠普", "华硕", "宏碁", "微软", "索尼",
            "松下", "LG", "海尔", "美的", "格力", "TCL", "创维", "海信",
            "飞利浦", "博世", "西门子", "夏普", "东芝", "佳能", "尼康", "富士",
            "JBL", "BOSE", "雷蛇", "罗技", "铁三角", "森海塞尔", "拜亚动力",
            "漫步者", "飞科", "奥克斯", "九阳", "苏泊尔", "小天鹅", "长虹",
            "康佳", "AOC", "明基", "HTC", "诺基亚", "摩托罗拉", "黑莓",
            "OPPO", "vivo", "realme", "红米", "iQOO", "努比亚", "中兴",
            "酷派", "金立", "锤子", "360", "坚果", "华为", "荣耀", "小米"
        ]
        
        # 将词汇添加到jieba词典中
        for term in electronic_terms + brand_terms:
            jieba.add_word(term)
    
    def extract_keywords(self, text, topk=20, allow_pos=('ns', 'n', 'vn', 'v', 'nr')):
        """
        从文本中提取关键词
        
        Args:
            text: 待分析的文本
            topk: 返回的关键词数量
            allow_pos: 允许的词性
            
        Returns:
            list: 关键词列表，每个元素为(word, weight)元组
        """
        # 使用TF-IDF算法提取关键词
        keywords_tfidf = jieba.analyse.extract_tags(
            text, 
            topK=topk, 
            withWeight=True,
            allowPOS=allow_pos
        )
        
        # 使用TextRank算法提取关键词
        keywords_textrank = jieba.analyse.textrank(
            text, 
            topK=topk, 
            withWeight=True,
            allowPOS=allow_pos
        )
        
        # 合并两种算法的结果，取权重平均值
        keywords_dict = {}
        for word, weight in keywords_tfidf:
            if word not in self.stopwords:
                keywords_dict[word] = weight
                
        for word, weight in keywords_textrank:
            if word not in self.stopwords:
                if word in keywords_dict:
                    keywords_dict[word] = (keywords_dict[word] + weight) / 2
                else:
                    keywords_dict[word] = weight
        
        # 按权重排序并返回topk个关键词
        keywords = sorted(keywords_dict.items(), key=lambda x: x[1], reverse=True)[:topk]
        return keywords
    
    def segment_text(self, text, remove_stopwords=True):
        """
        对文本进行分词
        
        Args:
            text: 待分词的文本
            remove_stopwords: 是否去除停用词
            
        Returns:
            list: 分词结果列表
        """
        # 使用jieba进行分词
        words = jieba.lcut(text)
        
        # 去除停用词
        if remove_stopwords:
            words = [word for word in words if word not in self.stopwords and len(word.strip()) > 0]
            
        return words
    
    def word_frequency(self, texts, top_n=50):
        """
        统计词频
        
        Args:
            texts: 文本列表或单个文本字符串
            top_n: 返回的高频词数量
            
        Returns:
            list: 词频统计结果，每个元素为(word, count)元组
        """
        if isinstance(texts, str):
            texts = [texts]
            
        all_words = []
        for text in texts:
            words = self.segment_text(text)
            all_words.extend(words)
            
        # 统计词频
        word_counts = Counter(all_words)
        
        # 返回出现频率最高的top_n个词
        return word_counts.most_common(top_n)
    
    def analyze_product_titles(self, titles):
        """
        分析产品标题，提取关键特征
        
        Args:
            titles: 产品标题列表
            
        Returns:
            dict: 分析结果，包含关键词、品牌、热门词等
        """
        if not titles:
            return {}
            
        # 合并所有标题文本
        all_text = ' '.join(titles)
        
        # 提取关键词
        keywords = self.extract_keywords(all_text, topk=30)
        
        # 统计词频
        word_freq = self.word_frequency(all_text, top_n=50)
        
        # 提取品牌信息
        brands = self._extract_brands(titles)
        
        # 提取产品类型
        product_types = self._extract_product_types(titles)
        
        return {
            'keywords': keywords,
            'word_frequency': word_freq,
            'brands': brands,
            'product_types': product_types
        }
    
    def _extract_brands(self, titles):
        """
        从产品标题中提取品牌信息
        
        Args:
            titles: 产品标题列表
            
        Returns:
            list: 品牌及其出现次数，按出现次数降序排列
        """
        # 常见电子产品品牌列表
        common_brands = [
            "华为", "小米", "OPPO", "vivo", "荣耀", "魅族", "一加", "三星",
            "苹果", "联想", "戴尔", "惠普", "华硕", "宏碁", "微软", "索尼",
            "松下", "LG", "海尔", "美的", "格力", "TCL", "创维", "海信",
            "飞利浦", "博世", "西门子", "夏普", "东芝", "佳能", "尼康", "富士",
            "JBL", "BOSE", "雷蛇", "罗技", "铁三角", "森海塞尔", "拜亚动力",
            "漫步者", "飞科", "奥克斯", "九阳", "苏泊尔", "小天鹅", "长虹",
            "康佳", "AOC", "明基", "HTC", "诺基亚", "摩托罗拉", "黑莓",
            "OPPO", "vivo", "realme", "红米", "iQOO", "努比亚", "中兴",
            "酷派", "金立", "锤子", "360", "坚果", "华为", "荣耀", "小米"
        ]
        
        # 统计品牌出现次数
        brand_counts = Counter()
        for title in titles:
            for brand in common_brands:
                if brand.lower() in title.lower() or brand in title:
                    brand_counts[brand] += 1
        
        # 返回按出现次数排序的品牌列表
        return brand_counts.most_common()
    
    def _extract_product_types(self, titles):
        """
        从产品标题中提取产品类型
        
        Args:
            titles: 产品标题列表
            
        Returns:
            list: 产品类型及其出现次数，按出现次数降序排列
        """
        # 常见电子产品类型
        product_types = [
            "手机", "电脑", "笔记本", "平板", "电视", "冰箱", "洗衣机", 
            "空调", "相机", "耳机", "音箱", "充电器", "移动电源", "路由器", 
            "智能手表", "智能手环", "VR眼镜", "游戏机", "无人机", "摄像头",
            "显示器", "键盘", "鼠标", "打印机", "扫描仪", "投影仪", "智能家居"
        ]
        
        # 统计产品类型出现次数
        type_counts = Counter()
        for title in titles:
            for p_type in product_types:
                if p_type in title:
                    type_counts[p_type] += 1
        
        # 返回按出现次数排序的产品类型列表
        return type_counts.most_common()

    def analyze_from_database(self, category_id=None, limit=1000):
        """
        从数据库中获取产品数据并进行文本分析
        
        Args:
            category_id: 分类ID，如果为None则分析所有分类
            limit: 最大分析的产品数量
            
        Returns:
            dict: 分析结果
        """
        session = get_db_session()
        try:
            # 查询产品数据
            query = session.query(Product)
            if category_id is not None:
                query = query.filter(Product.category_id == category_id)
            products = query.limit(limit).all()
            
            # 提取产品标题
            titles = [p.title for p in products if p.title]
            
            # 分析产品标题
            title_analysis = self.analyze_product_titles(titles)
            
            # 获取分类信息
            if category_id is not None:
                category = session.query(Category).filter(Category.id == category_id).first()
                category_name = category.name if category else "未知分类"
            else:
                category_name = "所有分类"
            
            # 构建分析结果
            result = {
                'category_name': category_name,
                'product_count': len(products),
                'title_analysis': title_analysis,
            }
            
            return result
        except Exception as e:
            logger.error(f"从数据库分析产品文本失败: {e}")
            return {}
        finally:
            close_db_session(session)


def main():
    """
    主函数，用于命令行调用
    """
    import argparse
    import json
    import os
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='淘宝电子产品文本分析工具')
    parser.add_argument('--category', type=int, help='分类ID')
    parser.add_argument('--limit', type=int, default=1000, help='分析的最大产品数量')
    parser.add_argument('--output', type=str, help='输出结果的JSON文件路径')
    parser.add_argument('--stopwords', type=str, help='停用词文件路径')
    
    args = parser.parse_args()
    
    # 创建文本分析器
    analyzer = TextAnalyzer(stopwords_path=args.stopwords)
    
    # 从数据库分析产品文本
    result = analyzer.analyze_from_database(category_id=args.category, limit=args.limit)
    
    # 输出结果
    if args.output:
        os.makedirs(os.path.dirname(os.path.abspath(args.output)), exist_ok=True)
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        logger.info(f"分析结果已保存到: {args.output}")
    else:
        print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == '__main__':
    main()
弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '弊端', '弊病', '弊端', '弊病', '弊端', '弊病', '弊端', '弊病',
        '