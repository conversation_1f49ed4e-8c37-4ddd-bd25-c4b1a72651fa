# -*- coding: utf-8 -*-
"""
淘宝爬虫实现类，用于爬取淘宝电子产品数据
"""

import re
import json
import time
import logging
from urllib.parse import quote
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from .base_crawler import BaseCrawler
from .user_behavior import UserBehavior

logger = logging.getLogger(__name__)


class TaobaoCrawler(BaseCrawler):
    """
    淘宝爬虫类，用于爬取淘宝电子产品数据
    """
    
    def __init__(self):
        """
        初始化淘宝爬虫
        """
        super().__init__()
        self.base_url = "https://s.taobao.com/search"
        self.user_behavior = UserBehavior(self.driver)
        self.login_status = False
    
    def login(self):
        """
        登录淘宝
        
        Returns:
            bool: 是否登录成功
        """
        if self.login_status:
            logger.info("已经登录，无需重复登录")
            return True
            
        try:
            # 访问淘宝首页
            self.visit_url("https://www.taobao.com/")
            
            # 等待页面加载完成
            time.sleep(2)
            
            # 检查是否已经登录
            if self._check_login_status():
                logger.info("检测到已经登录淘宝")
                self.login_status = True
                return True
            
            # 点击登录按钮
            try:
                login_link = self.driver.find_element(By.CSS_SELECTOR, ".btn-login")
                self.user_behavior.click_element(login_link)
            except NoSuchElementException:
                logger.info("未找到常规登录按钮，尝试其他登录入口")
                try:
                    login_link = self.driver.find_element(By.CSS_SELECTOR, ".h")
                    self.user_behavior.click_element(login_link)
                except NoSuchElementException:
                    logger.error("未找到任何登录入口")
                    return False
            
            # 等待扫码登录界面出现
            logger.info("等待扫码登录...")
            WebDriverWait(self.driver, 60).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".user"))
            )
            
            # 登录成功
            logger.info("登录成功")
            self.login_status = True
            return True
            
        except Exception as e:
            logger.error(f"登录过程中发生错误: {str(e)}")
            return False
    
    def _check_login_status(self):
        """
        检查是否已经登录
        
        Returns:
            bool: 是否已登录
        """
        try:
            # 查找登录后才会显示的元素
            self.driver.find_element(By.CSS_SELECTOR, ".user")
            return True
        except NoSuchElementException:
            return False
    
    def search_products(self, keyword, category=None, page=1):
        """
        搜索产品
        
        Args:
            keyword: 搜索关键词
            category: 产品类别
            page: 页码
            
        Returns:
            bool: 是否搜索成功
        """
        try:
            # 构建搜索URL
            # encoded_keyword = quote(keyword)
            # url = f"{self.base_url}?q={encoded_keyword}&s={(page-1)*44}"
            # if category:
            #     url += f"&cat={category}"

            encoded_keyword = quote(keyword)
            url = f"{self.base_url}?commend=all&page={page}&q={encoded_keyword}&tab=all"
            # if category:
            #     url += f"&cat={category}"
            
            # 访问搜索页面
            success = self.visit_url(url)
            if not success:
                return False
            
            # 等待搜索结果加载
            try:
                WebDriverWait(self.driver, self.timeout).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, "#content_items_wrapper .search-content-col"))
                )
                logger.info(f"成功加载搜索结果: {keyword}, 页码: {page}")
                return True
            except TimeoutException:
                logger.warning(f"等待搜索结果超时: {keyword}, 页码: {page}")
                return False
                
        except Exception as e:
            logger.error(f"搜索产品时发生错误: {str(e)}")
            return False
    
    def extract_product_data(self):
        """
        从当前页面提取产品数据
        
        Returns:
            list: 产品数据列表
        """
        products = []
        try:
            # 等待产品列表加载完成
            items = self.user_behavior.wait_for_elements("#content_items_wrapper .search-content-col", timeout=self.timeout)
            
            if not items:
                logger.warning("未找到产品列表")
                return products
            
            logger.info(f"找到 {len(items)} 个产品")
            
            # 模拟人类浏览行为，随机滚动页面
            self.user_behavior.simulate_human_browsing(duration=3)
            
            # 提取每个产品的数据
            for item in items:
                try:
                    # 基本信息
                    product_id = item.get_attribute("data-id")
                    title_element = item.find_element(By.CSS_SELECTOR, ".title a")
                    title = title_element.text.strip()
                    detail_url = title_element.get_attribute("href")
                    if detail_url and not detail_url.startswith("http"):
                        detail_url = "https:" + detail_url
                    
                    # 价格信息
                    price = ""
                    try:
                        price = item.find_element(By.CSS_SELECTOR, ".price strong").text.strip()
                    except NoSuchElementException:
                        try:
                            price = item.find_element(By.CSS_SELECTOR, ".price").text.strip()
                        except NoSuchElementException:
                            pass
                    
                    # 销量信息
                    sales = ""
                    try:
                        sales_text = item.find_element(By.CSS_SELECTOR, ".deal-cnt").text.strip()
                        # 提取销量数字
                        sales_match = re.search(r'\d+', sales_text)
                        if sales_match:
                            sales = sales_match.group()
                    except NoSuchElementException:
                        pass
                    
                    # 店铺信息
                    shop_name = ""
                    shop_url = ""
                    try:
                        shop_element = item.find_element(By.CSS_SELECTOR, ".shop a")
                        shop_name = shop_element.text.strip()
                        shop_url = shop_element.get_attribute("href")
                        if shop_url and not shop_url.startswith("http"):
                            shop_url = "https:" + shop_url
                    except NoSuchElementException:
                        pass
                    
                    # 位置信息
                    location = ""
                    try:
                        location = item.find_element(By.CSS_SELECTOR, ".location").text.strip()
                    except NoSuchElementException:
                        pass
                    
                    # 构建产品数据
                    product_data = {
                        "product_id": product_id,
                        "title": title,
                        "price": price,
                        "sales": sales,
                        "detail_url": detail_url,
                        "shop_name": shop_name,
                        "shop_url": shop_url,
                        "location": location,
                        "crawl_time": time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                    
                    products.append(product_data)
                    
                except Exception as e:
                    logger.warning(f"提取产品数据时发生错误: {str(e)}")
                    continue
            
            return products
            
        except Exception as e:
            logger.error(f"提取产品列表时发生错误: {str(e)}")
            return products
    
    def get_product_details(self, product_data):
        """
        获取产品详情
        
        Args:
            product_data: 基本产品数据
            
        Returns:
            dict: 增强后的产品数据
        """
        try:
            # 访问产品详情页
            if not self.visit_url(product_data["detail_url"]):
                return product_data
            
            # 等待详情页加载
            time.sleep(3)
            
            # 模拟人类浏览行为
            self.user_behavior.simulate_human_browsing(duration=5)
            
            # 提取详细信息
            try:
                # 商品属性
                attributes = {}
                try:
                    attribute_elements = self.driver.find_elements(By.CSS_SELECTOR, ".attributes-list li")
                    for element in attribute_elements:
                        attr_text = element.text.strip()
                        if ':' in attr_text:
                            key, value = attr_text.split(':', 1)
                            attributes[key.strip()] = value.strip()
                except NoSuchElementException:
                    pass
                
                # 评分信息
                rating = ""
                try:
                    rating_element = self.driver.find_element(By.CSS_SELECTOR, ".tb-rate-score")
                    rating = rating_element.text.strip()
                except NoSuchElementException:
                    pass
                
                # 评论数量
                review_count = ""
                try:
                    review_element = self.driver.find_element(By.CSS_SELECTOR, ".tm-count")
                    review_count = review_element.text.strip()
                except NoSuchElementException:
                    pass
                
                # 更新产品数据
                product_data.update({
                    "attributes": attributes,
                    "rating": rating,
                    "review_count": review_count
                })
                
            except Exception as e:
                logger.warning(f"提取产品详情时发生错误: {str(e)}")
            
            return product_data
            
        except Exception as e:
            logger.error(f"获取产品详情时发生错误: {str(e)}")
            return product_data
    
    def crawl(self, keywords, pages_per_keyword=5, get_details=False):
        """
        爬取产品数据
        
        Args:
            keywords: 关键词列表
            pages_per_keyword: 每个关键词爬取的页数
            get_details: 是否获取详细信息
            
        Returns:
            list: 产品数据列表
        """
        all_products = []
        
        # 尝试登录
        if not self.login():
            logger.warning("登录失败，将以未登录状态爬取数据，可能会受到限制")
        
        # 遍历关键词
        for keyword in keywords:
            logger.info(f"开始爬取关键词: {keyword}")
            
            # 遍历页码
            for page in range(1, pages_per_keyword + 1):
                logger.info(f"爬取 {keyword} 的第 {page} 页")
                
                # 搜索产品
                if not self.search_products(keyword, page=page):
                    logger.warning(f"搜索 {keyword} 的第 {page} 页失败，跳过")
                    continue
                
                # 提取产品数据
                products = self.extract_product_data()
                logger.info(f"从 {keyword} 的第 {page} 页提取到 {len(products)} 个产品")
                
                # 获取详细信息
                if get_details and products:
                    logger.info(f"开始获取 {len(products)} 个产品的详细信息")
                    detailed_products = []
                    for i, product in enumerate(products):
                        logger.info(f"获取第 {i+1}/{len(products)} 个产品的详细信息")
                        detailed_product = self.get_product_details(product)
                        detailed_products.append(detailed_product)
                        # 添加随机延迟，避免频繁请求
                        self.random_sleep(2, 5)
                    products = detailed_products
                
                # 添加关键词信息
                for product in products:
                    product["keyword"] = keyword
                
                # 合并结果
                all_products.extend(products)
                
                # 页面间随机延迟
                self.random_sleep(3, 8)
        
        logger.info(f"爬取完成，共获取 {len(all_products)} 个产品数据")
        return all_products