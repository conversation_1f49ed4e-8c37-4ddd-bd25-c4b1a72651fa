# -*- coding: utf-8 -*-
"""
用户行为模拟模块，用于模拟人类操作浏览器的行为
"""

import time
import random
import logging
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, ElementNotInteractableException

logger = logging.getLogger(__name__)

class UserBehavior:
    """
    用户行为模拟类，提供各种模拟人类操作的方法
    """
    
    def __init__(self, driver):
        """
        初始化用户行为模拟器
        
        Args:
            driver: Selenium WebDriver实例
        """
        self.driver = driver
        self.actions = ActionChains(driver)
    
    def random_sleep(self, min_time=1, max_time=3):
        """
        随机等待一段时间
        
        Args:
            min_time: 最小等待时间（秒）
            max_time: 最大等待时间（秒）
        """
        sleep_time = random.uniform(min_time, max_time)
        time.sleep(sleep_time)
    
    def move_to_element(self, element, random_offset=True):
        """
        将鼠标移动到元素上，可以添加随机偏移
        
        Args:
            element: 目标元素
            random_offset: 是否添加随机偏移
        """
        try:
            if random_offset:
                # 获取元素大小
                size = element.size
                # 计算随机偏移量，使鼠标指向元素内的随机位置
                offset_x = random.randint(5, max(6, size['width'] - 5))
                offset_y = random.randint(5, max(6, size['height'] - 5))
                # 移动到元素上的随机位置
                self.actions.move_to_element_with_offset(element, offset_x, offset_y).perform()
            else:
                # 直接移动到元素中心
                self.actions.move_to_element(element).perform()
            
            self.random_sleep(0.5, 1.5)
            return True
        except Exception as e:
            logger.warning(f"移动到元素失败: {str(e)}")
            return False
    
    def click_element(self, element, random_delay=True):
        """
        模拟人类点击元素
        
        Args:
            element: 要点击的元素
            random_delay: 是否在点击前添加随机延迟
        
        Returns:
            bool: 是否点击成功
        """
        try:
            # 先移动到元素上
            self.move_to_element(element)
            
            # 添加随机延迟
            if random_delay:
                self.random_sleep(0.1, 0.5)
            
            # 点击元素
            element.click()
            self.random_sleep(0.5, 1.5)
            return True
        except (ElementNotInteractableException, NoSuchElementException) as e:
            logger.warning(f"点击元素失败: {str(e)}")
            # 尝试使用JavaScript点击
            try:
                self.driver.execute_script("arguments[0].click();", element)
                self.random_sleep(0.5, 1.5)
                return True
            except Exception as js_e:
                logger.error(f"JavaScript点击元素也失败: {str(js_e)}")
                return False
        except Exception as e:
            logger.error(f"点击元素时发生未知错误: {str(e)}")
            return False
    
    def scroll_page(self, direction="down", distance=None, speed="medium"):
        """
        模拟滚动页面
        
        Args:
            direction: 滚动方向，"up"或"down"
            distance: 滚动距离，如果为None则随机滚动
            speed: 滚动速度，"slow", "medium"或"fast"
        """
        # 确定滚动距离
        if distance is None:
            # 随机滚动距离，范围为100-800像素
            distance = random.randint(100, 800)
        
        # 根据方向调整滚动值的正负
        if direction == "up":
            distance = -distance
        
        # 确定滚动速度（每步滚动的距离和每步之间的延迟）
        if speed == "slow":
            step_size = 20
            step_delay = 0.025
        elif speed == "medium":
            step_size = 40
            step_delay = 0.015
        else:  # fast
            step_size = 80
            step_delay = 0.01
        
        # 计算需要滚动的步数
        steps = abs(distance) // step_size
        if abs(distance) % step_size > 0:
            steps += 1
        
        # 分步滚动，模拟人类滚动行为
        scrolled = 0
        for _ in range(steps):
            remaining = abs(distance) - scrolled
            if remaining <= 0:
                break
                
            step = min(step_size, remaining)
            if distance < 0:
                step = -step
                
            self.driver.execute_script(f"window.scrollBy(0, {step});")
            scrolled += abs(step)
            time.sleep(step_delay)
        
        # 滚动后短暂停顿
        self.random_sleep(0.5, 1.0)
    
    def type_text(self, element, text, clear_first=True, typing_speed="medium"):
        """
        模拟人类输入文本
        
        Args:
            element: 要输入文本的元素
            text: 要输入的文本
            clear_first: 是否先清除元素中的现有文本
            typing_speed: 打字速度，"slow", "medium"或"fast"
        
        Returns:
            bool: 是否输入成功
        """
        try:
            # 先点击元素获取焦点
            self.click_element(element)
            
            # 清除现有文本
            if clear_first:
                element.clear()
                self.random_sleep(0.2, 0.5)
            
            # 确定打字速度（每个字符之间的延迟）
            if typing_speed == "slow":
                min_delay, max_delay = 0.1, 0.3
            elif typing_speed == "medium":
                min_delay, max_delay = 0.05, 0.15
            else:  # fast
                min_delay, max_delay = 0.01, 0.05
            
            # 逐个字符输入，模拟人类打字
            for char in text:
                element.send_keys(char)
                # 随机延迟，模拟打字节奏
                time.sleep(random.uniform(min_delay, max_delay))
            
            # 输入完成后短暂停顿
            self.random_sleep(0.3, 0.8)
            return True
        except Exception as e:
            logger.error(f"输入文本失败: {str(e)}")
            return False
    
    def wait_for_element(self, locator, by=By.CSS_SELECTOR, timeout=10):
        """
        等待元素出现
        
        Args:
            locator: 元素定位器
            by: 定位方式
            timeout: 超时时间（秒）
        
        Returns:
            element: 找到的元素，如果未找到则返回None
        """
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, locator))
            )
            return element
        except TimeoutException:
            logger.warning(f"等待元素 {locator} 超时")
            return None
    
    def wait_for_elements(self, locator, by=By.CSS_SELECTOR, timeout=10):
        """
        等待多个元素出现
        
        Args:
            locator: 元素定位器
            by: 定位方式
            timeout: 超时时间（秒）
        
        Returns:
            elements: 找到的元素列表，如果未找到则返回空列表
        """
        try:
            elements = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_all_elements_located((by, locator))
            )
            return elements
        except TimeoutException:
            logger.warning(f"等待元素组 {locator} 超时")
            return []
    
    def random_mouse_movement(self, num_movements=3):
        """
        在页面上进行随机鼠标移动
        
        Args:
            num_movements: 移动次数
        """
        # 获取窗口大小
        window_size = self.driver.get_window_size()
        width = window_size['width']
        height = window_size['height']
        
        # 进行随机鼠标移动
        for _ in range(num_movements):
            # 生成随机目标位置
            target_x = random.randint(0, width)
            target_y = random.randint(0, height)
            
            # 移动鼠标
            self.actions.move_by_offset(target_x, target_y).perform()
            
            # 重置鼠标位置（避免移出窗口）
            self.actions.move_to_element(self.driver.find_element(By.TAG_NAME, "body")).perform()
            
            # 随机等待
            self.random_sleep(0.2, 0.7)
    
    def simulate_human_browsing(self, duration=5):
        """
        模拟人类浏览行为，包括滚动、暂停等
        
        Args:
            duration: 模拟浏览的大致时长（秒）
        """
        start_time = time.time()
        
        while time.time() - start_time < duration:
            # 随机选择一个行为
            action = random.choice(["scroll", "pause", "mouse_move"])
            
            if action == "scroll":
                # 随机滚动
                direction = random.choice(["down", "up"])
                # 向下滚动的概率更高
                if direction == "up" and random.random() < 0.7:
                    direction = "down"
                self.scroll_page(direction=direction)
            
            elif action == "pause":
                # 随机暂停，模拟阅读
                self.random_sleep(0.5, 2.0)
            
            elif action == "mouse_move":
                # 随机鼠标移动
                self.random_mouse_movement(num_movements=1)