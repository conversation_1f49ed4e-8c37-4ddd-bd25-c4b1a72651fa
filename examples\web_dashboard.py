# -*- coding: utf-8 -*-
"""
电商数据可视化Web仪表盘

该脚本使用Flask创建一个Web应用程序，展示电商数据的可视化结果
"""

import os
import sys
import json
import logging
from datetime import datetime
from flask import Flask, render_template, request, jsonify

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入数据分析和可视化模块
from data.data_analysis import DataAnalyzer
from data.data_visualization import get_visualizer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("web_dashboard.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

# 创建Flask应用
app = Flask(__name__, 
           static_folder=os.path.join(os.path.dirname(__file__), "../data/visualization"),
           template_folder=os.path.join(os.path.dirname(__file__), "../templates"))

# 创建输出目录
output_dir = os.path.join(os.path.dirname(__file__), "../data/visualization/dashboard")
os.makedirs(output_dir, exist_ok=True)

# 初始化数据分析器和可视化器
analyzer = DataAnalyzer()
visualizer = get_visualizer(output_dir)

@app.route('/')
def index():
    """
    首页路由
    """
    # 获取所有分类
    categories = analyzer.get_category_stats()
    category_names = [category['name'] for category in categories]
    
    # 获取汇总统计信息
    summary_stats = analyzer.get_summary_stats()
    
    return render_template('dashboard.html', 
                           categories=category_names,
                           summary_stats=summary_stats)

@app.route('/api/categories')
def get_categories():
    """
    获取所有分类
    """
    categories = analyzer.get_category_stats()
    return jsonify(categories)

@app.route('/api/summary')
def get_summary():
    """
    获取汇总统计信息
    """
    summary_stats = analyzer.get_summary_stats()
    return jsonify(summary_stats)

@app.route('/api/price_distribution')
def get_price_distribution():
    """
    获取价格分布数据
    """
    category = request.args.get('category', None)
    price_distribution = analyzer.get_price_distribution(category)
    return jsonify(price_distribution)

@app.route('/api/price_trend')
def get_price_trend():
    """
    获取价格趋势数据
    """
    category = request.args.get('category', None)
    days = int(request.args.get('days', 30))
    price_trend = analyzer.get_price_trend(category, days)
    return jsonify(price_trend)

@app.route('/api/sales_trend')
def get_sales_trend():
    """
    获取销量趋势数据
    """
    category = request.args.get('category', None)
    days = int(request.args.get('days', 30))
    sales_trend = analyzer.get_sales_trend(category, days)
    return jsonify(sales_trend)

@app.route('/api/hot_products')
def get_hot_products():
    """
    获取热门产品数据
    """
    category = request.args.get('category', None)
    limit = int(request.args.get('limit', 10))
    hot_products = analyzer.get_top_products(category, limit)
    return jsonify(hot_products)

@app.route('/api/shop_stats')
def get_shop_stats():
    """
    获取店铺统计数据
    """
    limit = int(request.args.get('limit', 10))
    shop_stats = analyzer.get_shop_stats(limit)
    return jsonify(shop_stats)

@app.route('/api/location_stats')
def get_location_stats():
    """
    获取地区统计数据
    """
    limit = int(request.args.get('limit', 10))
    location_stats = analyzer.get_location_stats(limit)
    return jsonify(location_stats)

@app.route('/api/price_sales_scatter')
def get_price_sales_scatter():
    """
    获取价格-销量散点图数据
    """
    category = request.args.get('category', None)
    limit = int(request.args.get('limit', 100))
    scatter_data = analyzer.get_price_sales_scatter(category, limit)
    return jsonify(scatter_data)

@app.route('/api/generate_charts')
def generate_charts():
    """
    生成所有图表
    """
    category = request.args.get('category', None)
    days = int(request.args.get('days', 30))
    
    try:
        # 生成仪表盘图表
        charts = visualizer.generate_dashboard_charts(category, days)
        
        # 构建图表URL
        chart_urls = {}
        for chart_name, filename in charts.items():
            if filename:
                chart_urls[chart_name] = f"/dashboard/{filename}"
        
        return jsonify({
            'success': True,
            'charts': chart_urls
        })
    except Exception as e:
        logger.error(f"生成图表时发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

def main():
    """
    主函数
    """
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="电商数据可视化Web仪表盘")
    parser.add_argument("--host", default="127.0.0.1", help="主机地址")
    parser.add_argument("--port", type=int, default=5000, help="端口号")
    parser.add_argument("--debug", action="store_true", help="是否开启调试模式")
    
    args = parser.parse_args()
    
    # 检查模板目录是否存在
    template_dir = os.path.join(os.path.dirname(__file__), "../templates")
    if not os.path.exists(template_dir):
        os.makedirs(template_dir, exist_ok=True)
        logger.warning(f"模板目录不存在，已创建: {template_dir}")
        
        # 创建一个简单的仪表盘模板
        create_dashboard_template(template_dir)
    
    # 启动Flask应用
    logger.info(f"启动Web仪表盘，地址: {args.host}:{args.port}")
    app.run(host=args.host, port=args.port, debug=args.debug)

def create_dashboard_template(template_dir):
    """
    创建仪表盘模板
    
    Args:
        template_dir: 模板目录
    """
    dashboard_html = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商数据分析仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .stats-card {
            text-align: center;
            padding: 15px;
        }
        .stats-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
        }
        .stats-card p {
            color: #6c757d;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">电商数据分析仪表盘</h1>
        
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="form-group">
                    <label for="categorySelect">选择分类:</label>
                    <select class="form-control" id="categorySelect">
                        <option value="">所有分类</option>
                        {% for category in categories %}
                        <option value="{{ category }}">{{ category }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="daysSelect">时间范围:</label>
                    <select class="form-control" id="daysSelect">
                        <option value="7">最近7天</option>
                        <option value="30" selected>最近30天</option>
                        <option value="90">最近90天</option>
                        <option value="180">最近180天</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="limitSelect">显示数量:</label>
                    <select class="form-control" id="limitSelect">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button class="btn btn-primary form-control" id="refreshBtn">刷新数据</button>
                </div>
            </div>
        </div>
        
        <!-- 汇总统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <h3 id="totalProducts">{{ summary_stats.total_products }}</h3>
                    <p>总产品数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <h3 id="totalCategories">{{ summary_stats.total_categories }}</h3>
                    <p>分类数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <h3 id="totalShops">{{ summary_stats.total_shops }}</h3>
                    <p>店铺数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <h3 id="avgPrice">¥{{ "%.2f"|format(summary_stats.avg_price) }}</h3>
                    <p>平均价格</p>
                </div>
            </div>
        </div>
        
        <!-- 图表区域 -->
        <div class="row">
            <!-- 价格分布图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">价格分布</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="priceDistributionChart" class="img-fluid" alt="价格分布图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 价格趋势图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">价格趋势</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="priceTrendChart" class="img-fluid" alt="价格趋势图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 销量趋势图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">销量趋势</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="salesTrendChart" class="img-fluid" alt="销量趋势图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 价格-销量散点图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">价格-销量关系</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="priceSalesScatterChart" class="img-fluid" alt="价格-销量散点图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分类统计图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">分类产品数量</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="categoryCountChart" class="img-fluid" alt="分类产品数量图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分类平均价格图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">分类平均价格</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="categoryPriceChart" class="img-fluid" alt="分类平均价格图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 店铺统计图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">热门店铺</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="shopStatsChart" class="img-fluid" alt="店铺统计图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 地区统计图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">地区分布</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="locationStatsChart" class="img-fluid" alt="地区统计图">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 热门产品表格 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">热门产品</div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>产品名称</th>
                                        <th>价格</th>
                                        <th>销量</th>
                                        <th>店铺</th>
                                        <th>评分</th>
                                    </tr>
                                </thead>
                                <tbody id="hotProductsTable">
                                    <!-- 热门产品数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始加载数据
            refreshData();
            
            // 刷新按钮点击事件
            document.getElementById('refreshBtn').addEventListener('click', function() {
                refreshData();
            });
            
            // 分类选择变化事件
            document.getElementById('categorySelect').addEventListener('change', function() {
                refreshData();
            });
            
            // 时间范围选择变化事件
            document.getElementById('daysSelect').addEventListener('change', function() {
                refreshData();
            });
            
            // 显示数量选择变化事件
            document.getElementById('limitSelect').addEventListener('change', function() {
                refreshData();
            });
        });
        
        // 刷新数据
        function refreshData() {
            // 获取选择的参数
            const category = document.getElementById('categorySelect').value;
            const days = document.getElementById('daysSelect').value;
            const limit = document.getElementById('limitSelect').value;
            
            // 构建查询参数
            const params = new URLSearchParams();
            if (category) params.append('category', category);
            params.append('days', days);
            params.append('limit', limit);
            
            // 更新汇总统计信息
            fetch('/api/summary')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('totalProducts').textContent = data.total_products;
                    document.getElementById('totalCategories').textContent = data.total_categories;
                    document.getElementById('totalShops').textContent = data.total_shops;
                    document.getElementById('avgPrice').textContent = '¥' + data.avg_price.toFixed(2);
                })
                .catch(error => console.error('获取汇总统计信息失败:', error));
            
            // 生成图表
            fetch(`/api/generate_charts?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新图表
                        if (data.charts.price_distribution) {
                            document.getElementById('priceDistributionChart').src = data.charts.price_distribution;
                        }
                        if (data.charts.price_trend) {
                            document.getElementById('priceTrendChart').src = data.charts.price_trend;
                        }
                        if (data.charts.sales_trend) {
                            document.getElementById('salesTrendChart').src = data.charts.sales_trend;
                        }
                        if (data.charts.price_sales_scatter) {
                            document.getElementById('priceSalesScatterChart').src = data.charts.price_sales_scatter;
                        }
                        if (data.charts.category_count) {
                            document.getElementById('categoryCountChart').src = data.charts.category_count;
                        }
                        if (data.charts.category_price) {
                            document.getElementById('categoryPriceChart').src = data.charts.category_price;
                        }
                        if (data.charts.shop_stats) {
                            document.getElementById('shopStatsChart').src = data.charts.shop_stats;
                        }
                        if (data.charts.location_stats) {
                            document.getElementById('locationStatsChart').src = data.charts.location_stats;
                        }
                    } else {
                        console.error('生成图表失败:', data.error);
                    }
                })
                .catch(error => console.error('生成图表请求失败:', error));
            
            // 获取热门产品数据
            fetch(`/api/hot_products?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    const tableBody = document.getElementById('hotProductsTable');
                    tableBody.innerHTML = '';
                    
                    data.forEach((product, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${product.title}</td>
                            <td>¥${product.price.toFixed(2)}</td>
                            <td>${product.sales}</td>
                            <td>${product.shop_name}</td>
                            <td>${product.rating ? product.rating.toFixed(1) : 'N/A'}</td>
                        `;
                        tableBody.appendChild(row);
                    });
                })
                .catch(error => console.error('获取热门产品数据失败:', error));
        }
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    """
    
    # 写入模板文件
    with open(os.path.join(template_dir, 'dashboard.html'), 'w', encoding='utf-8') as f:
        f.write(dashboard_html)
    
    logger.info(f"已创建仪表盘模板: {os.path.join(template_dir, 'dashboard.html')}")

if __name__ == "__main__":
    main()