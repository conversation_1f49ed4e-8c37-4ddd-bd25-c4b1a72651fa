
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商数据分析仪表盘</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            padding-top: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .stats-card {
            text-align: center;
            padding: 15px;
        }
        .stats-card h3 {
            font-size: 2rem;
            margin-bottom: 5px;
        }
        .stats-card p {
            color: #6c757d;
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">电商数据分析仪表盘</h1>
        
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="form-group">
                    <label for="categorySelect">选择分类:</label>
                    <select class="form-control" id="categorySelect">
                        <option value="">所有分类</option>
                        {% for category in categories %}
                        <option value="{{ category }}">{{ category }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="daysSelect">时间范围:</label>
                    <select class="form-control" id="daysSelect">
                        <option value="7">最近7天</option>
                        <option value="30" selected>最近30天</option>
                        <option value="90">最近90天</option>
                        <option value="180">最近180天</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label for="limitSelect">显示数量:</label>
                    <select class="form-control" id="limitSelect">
                        <option value="5">5</option>
                        <option value="10" selected>10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>&nbsp;</label>
                    <button class="btn btn-primary form-control" id="refreshBtn">刷新数据</button>
                </div>
            </div>
        </div>
        
        <!-- 汇总统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <h3 id="totalProducts">{{ summary_stats.total_products }}</h3>
                    <p>总产品数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <h3 id="totalCategories">{{ summary_stats.total_categories }}</h3>
                    <p>分类数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <h3 id="totalShops">{{ summary_stats.total_shops }}</h3>
                    <p>店铺数</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <h3 id="avgPrice">¥{{ "%.2f"|format(summary_stats.avg_price) }}</h3>
                    <p>平均价格</p>
                </div>
            </div>
        </div>
        
        <!-- 图表区域 -->
        <div class="row">
            <!-- 价格分布图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">价格分布</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="priceDistributionChart" class="img-fluid" alt="价格分布图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 价格趋势图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">价格趋势</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="priceTrendChart" class="img-fluid" alt="价格趋势图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 销量趋势图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">销量趋势</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="salesTrendChart" class="img-fluid" alt="销量趋势图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 价格-销量散点图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">价格-销量关系</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="priceSalesScatterChart" class="img-fluid" alt="价格-销量散点图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分类统计图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">分类产品数量</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="categoryCountChart" class="img-fluid" alt="分类产品数量图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 分类平均价格图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">分类平均价格</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="categoryPriceChart" class="img-fluid" alt="分类平均价格图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 店铺统计图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">热门店铺</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="shopStatsChart" class="img-fluid" alt="店铺统计图">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 地区统计图 -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">地区分布</div>
                    <div class="card-body">
                        <div class="chart-container">
                            <img id="locationStatsChart" class="img-fluid" alt="地区统计图">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 热门产品表格 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">热门产品</div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>产品名称</th>
                                        <th>价格</th>
                                        <th>销量</th>
                                        <th>店铺</th>
                                        <th>评分</th>
                                    </tr>
                                </thead>
                                <tbody id="hotProductsTable">
                                    <!-- 热门产品数据将通过JavaScript填充 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 初始加载数据
            refreshData();
            
            // 刷新按钮点击事件
            document.getElementById('refreshBtn').addEventListener('click', function() {
                refreshData();
            });
            
            // 分类选择变化事件
            document.getElementById('categorySelect').addEventListener('change', function() {
                refreshData();
            });
            
            // 时间范围选择变化事件
            document.getElementById('daysSelect').addEventListener('change', function() {
                refreshData();
            });
            
            // 显示数量选择变化事件
            document.getElementById('limitSelect').addEventListener('change', function() {
                refreshData();
            });
        });
        
        // 刷新数据
        function refreshData() {
            // 获取选择的参数
            const category = document.getElementById('categorySelect').value;
            const days = document.getElementById('daysSelect').value;
            const limit = document.getElementById('limitSelect').value;
            
            // 构建查询参数
            const params = new URLSearchParams();
            if (category) params.append('category', category);
            params.append('days', days);
            params.append('limit', limit);
            
            // 更新汇总统计信息
            fetch('/api/summary')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('totalProducts').textContent = data.total_products;
                    document.getElementById('totalCategories').textContent = data.total_categories;
                    document.getElementById('totalShops').textContent = data.total_shops;
                    document.getElementById('avgPrice').textContent = '¥' + data.avg_price.toFixed(2);
                })
                .catch(error => console.error('获取汇总统计信息失败:', error));
            
            // 生成图表
            fetch(`/api/generate_charts?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新图表
                        if (data.charts.price_distribution) {
                            document.getElementById('priceDistributionChart').src = data.charts.price_distribution;
                        }
                        if (data.charts.price_trend) {
                            document.getElementById('priceTrendChart').src = data.charts.price_trend;
                        }
                        if (data.charts.sales_trend) {
                            document.getElementById('salesTrendChart').src = data.charts.sales_trend;
                        }
                        if (data.charts.price_sales_scatter) {
                            document.getElementById('priceSalesScatterChart').src = data.charts.price_sales_scatter;
                        }
                        if (data.charts.category_count) {
                            document.getElementById('categoryCountChart').src = data.charts.category_count;
                        }
                        if (data.charts.category_price) {
                            document.getElementById('categoryPriceChart').src = data.charts.category_price;
                        }
                        if (data.charts.shop_stats) {
                            document.getElementById('shopStatsChart').src = data.charts.shop_stats;
                        }
                        if (data.charts.location_stats) {
                            document.getElementById('locationStatsChart').src = data.charts.location_stats;
                        }
                    } else {
                        console.error('生成图表失败:', data.error);
                    }
                })
                .catch(error => console.error('生成图表请求失败:', error));
            
            // 获取热门产品数据
            fetch(`/api/hot_products?${params.toString()}`)
                .then(response => response.json())
                .then(data => {
                    const tableBody = document.getElementById('hotProductsTable');
                    tableBody.innerHTML = '';
                    
                    data.forEach((product, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td>${product.title}</td>
                            <td>¥${product.price.toFixed(2)}</td>
                            <td>${product.sales}</td>
                            <td>${product.shop_name}</td>
                            <td>${product.rating ? product.rating.toFixed(1) : 'N/A'}</td>
                        `;
                        tableBody.appendChild(row);
                    });
                })
                .catch(error => console.error('获取热门产品数据失败:', error));
        }
    </script>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    