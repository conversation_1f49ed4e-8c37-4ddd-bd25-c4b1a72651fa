# -*- coding: utf-8 -*-
"""
高级数据可视化模块，用于生成交互式图表和仪表盘
"""

import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.font_manager import FontProperties
import matplotlib as mpl
from sqlalchemy import func
import json
from datetime import datetime, timedelta

# 尝试导入Plotly和Dash相关库
try:
    import plotly.express as px
    import plotly.graph_objects as go
    import plotly.figure_factory as ff
    from plotly.subplots import make_subplots
    import plotly.io as pio
    
    # 设置Plotly主题
    pio.templates.default = "plotly_white"
    
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    logging.warning("Plotly库未安装，部分高级可视化功能将不可用")

from data.db_connection import get_db_session, close_db_session
from data.models import Product, Category, Shop, PriceHistory, SalesHistory, RawData
from analysis.basic_analysis import BasicAnalyzer
from analysis.advanced_analysis import AdvancedAnalyzer
from analysis.text_analysis import TextAnalyzer
from analysis.sentiment_analysis import SentimentAnalyzer

# 配置日志
logger = logging.getLogger(__name__)


class AdvancedCharts:
    """
    高级图表生成类，用于生成交互式图表和仪表盘
    """
    
    def __init__(self, output_dir="../output/advanced_charts"):
        """
        初始化高级图表生成器
        
        Args:
            output_dir: 图表输出目录
        """
        self.output_dir = output_dir
        self.basic_analyzer = BasicAnalyzer()
        self.advanced_analyzer = AdvancedAnalyzer()
        self.text_analyzer = TextAnalyzer()
        self.sentiment_analyzer = SentimentAnalyzer()
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 检查Plotly是否可用
        if not PLOTLY_AVAILABLE:
            logger.warning("Plotly库未安装，部分高级可视化功能将不可用")
    
    def save_plotly_figure(self, fig, filename):
        """
        保存Plotly图表
        
        Args:
            fig: Plotly图表对象
            filename: 文件名
        """
        if not PLOTLY_AVAILABLE:
            logger.error("Plotly库未安装，无法保存交互式图表")
            return
        
        filepath = os.path.join(self.output_dir, filename)
        try:
            # 保存为HTML（交互式）
            html_path = f"{filepath}.html"
            fig.write_html(html_path)
            
            # 保存为图片（静态）
            img_path = f"{filepath}.png"
            fig.write_image(img_path)
            
            logger.info(f"交互式图表已保存到: {html_path}")
            logger.info(f"静态图表已保存到: {img_path}")
        except Exception as e:
            logger.error(f"保存图表失败: {e}")
    
    def plot_price_trend(self, category_id=None, top_n=10, days=30):
        """
        绘制价格趋势图
        
        Args:
            category_id: 分类ID，如果为None则分析所有分类
            top_n: 显示的热门产品数量
            days: 分析的天数
        """
        if not PLOTLY_AVAILABLE:
            logger.error("Plotly库未安装，无法生成交互式价格趋势图")
            return None
        
        try:
            # 获取价格趋势数据
            price_trends = self.advanced_analyzer.analyze_price_trends(category_id, top_n, days)
            
            # 创建图表
            fig = go.Figure()
            
            # 为每个产品添加一条线
            for product in price_trends['products']:
                dates = [item['date'] for item in product['price_history']]
                prices = [item['price'] for item in product['price_history']]
                
                fig.add_trace(go.Scatter(
                    x=dates,
                    y=prices,
                    mode='lines+markers',
                    name=product['title'][:30] + '...' if len(product['title']) > 30 else product['title'],
                    hovertemplate='%{y:.2f}元<br>%{x}'
                ))
            
            # 设置图表布局
            title = f"{price_trends['category_name']}类热门产品价格趋势 (Top {top_n})" if category_id else f"热门产品价格趋势 (Top {top_n})"
            
            fig.update_layout(
                title=title,
                xaxis_title="日期",
                yaxis_title="价格 (元)",
                hovermode="closest",
                legend_title="产品",
                height=600,
                width=1000
            )
            
            # 保存图表
            filename = f"price_trend_{category_id}" if category_id else "price_trend_all"
            self.save_plotly_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制价格趋势图失败: {e}")
            return None
    
    def plot_sales_trend(self, category_id=None, top_n=10, days=30):
        """
        绘制销量趋势图
        
        Args:
            category_id: 分类ID，如果为None则分析所有分类
            top_n: 显示的热门产品数量
            days: 分析的天数
        """
        if not PLOTLY_AVAILABLE:
            logger.error("Plotly库未安装，无法生成交互式销量趋势图")
            return None
        
        try:
            # 获取销量趋势数据
            sales_trends = self.advanced_analyzer.analyze_sales_trends(category_id, top_n, days)
            
            # 创建图表
            fig = go.Figure()
            
            # 为每个产品添加一条线
            for product in sales_trends['products']:
                dates = [item['date'] for item in product['sales_history']]
                sales = [item['sales'] for item in product['sales_history']]
                
                fig.add_trace(go.Scatter(
                    x=dates,
                    y=sales,
                    mode='lines+markers',
                    name=product['title'][:30] + '...' if len(product['title']) > 30 else product['title'],
                    hovertemplate='%{y}件<br>%{x}'
                ))
            
            # 设置图表布局
            title = f"{sales_trends['category_name']}类热门产品销量趋势 (Top {top_n})" if category_id else f"热门产品销量趋势 (Top {top_n})"
            
            fig.update_layout(
                title=title,
                xaxis_title="日期",
                yaxis_title="销量 (件)",
                hovermode="closest",
                legend_title="产品",
                height=600,
                width=1000
            )
            
            # 保存图表
            filename = f"sales_trend_{category_id}" if category_id else "sales_trend_all"
            self.save_plotly_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制销量趋势图失败: {e}")
            return None
    
    def plot_brand_analysis(self, category_id=None, top_n=20):
        """
        绘制品牌分析图
        
        Args:
            category_id: 分类ID，如果为None则分析所有分类
            top_n: 显示的热门品牌数量
        """
        if not PLOTLY_AVAILABLE:
            logger.error("Plotly库未安装，无法生成交互式品牌分析图")
            return None
        
        try:
            # 获取品牌分析数据
            brand_analysis = self.advanced_analyzer.analyze_brands(category_id, top_n)
            
            # 创建子图
            fig = make_subplots(
                rows=2, cols=2,
                specs=[[{"type": "bar"}, {"type": "pie"}],
                       [{"type": "bar"}, {"type": "bar"}]],
                subplot_titles=("品牌产品数量", "品牌市场份额", "品牌平均价格", "品牌平均销量")
            )
            
            # 准备数据
            brands = [item['brand'] for item in brand_analysis['brands']]
            product_counts = [item['product_count'] for item in brand_analysis['brands']]
            market_shares = [item['market_share'] for item in brand_analysis['brands']]
            avg_prices = [item['avg_price'] for item in brand_analysis['brands']]
            avg_sales = [item['avg_sales'] for item in brand_analysis['brands']]
            
            # 添加品牌产品数量条形图
            fig.add_trace(
                go.Bar(x=brands, y=product_counts, name="产品数量", marker_color='rgb(55, 83, 109)'),
                row=1, col=1
            )
            
            # 添加品牌市场份额饼图
            fig.add_trace(
                go.Pie(labels=brands, values=market_shares, name="市场份额"),
                row=1, col=2
            )
            
            # 添加品牌平均价格条形图
            fig.add_trace(
                go.Bar(x=brands, y=avg_prices, name="平均价格", marker_color='rgb(26, 118, 255)'),
                row=2, col=1
            )
            
            # 添加品牌平均销量条形图
            fig.add_trace(
                go.Bar(x=brands, y=avg_sales, name="平均销量", marker_color='rgb(33, 150, 83)'),
                row=2, col=2
            )
            
            # 更新布局
            title = f"{brand_analysis['category_name']}类品牌分析 (Top {top_n})" if category_id else f"品牌分析 (Top {top_n})"
            
            fig.update_layout(
                title_text=title,
                height=800,
                width=1200,
                showlegend=False
            )
            
            # 更新坐标轴标签
            fig.update_xaxes(title_text="品牌", row=1, col=1)
            fig.update_yaxes(title_text="产品数量", row=1, col=1)
            
            fig.update_xaxes(title_text="品牌", row=2, col=1)
            fig.update_yaxes(title_text="平均价格 (元)", row=2, col=1)
            
            fig.update_xaxes(title_text="品牌", row=2, col=2)
            fig.update_yaxes(title_text="平均销量", row=2, col=2)
            
            # 保存图表
            filename = f"brand_analysis_{category_id}" if category_id else "brand_analysis_all"
            self.save_plotly_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制品牌分析图失败: {e}")
            return None
    
    def plot_seasonal_analysis(self, category_id=None, period='month'):
        """
        绘制季节性分析图
        
        Args:
            category_id: 分类ID，如果为None则分析所有分类
            period: 周期，可选值为'day'、'week'、'month'、'quarter'
        """
        if not PLOTLY_AVAILABLE:
            logger.error("Plotly库未安装，无法生成交互式季节性分析图")
            return None
        
        try:
            # 获取季节性分析数据
            seasonal_analysis = self.advanced_analyzer.analyze_seasonality(category_id, period)
            
            # 创建子图
            fig = make_subplots(
                rows=2, cols=1,
                specs=[[{"type": "scatter"}], [{"type": "bar"}]],
                subplot_titles=("价格季节性变化", "销量季节性变化"),
                vertical_spacing=0.1
            )
            
            # 准备数据
            periods = [item['period'] for item in seasonal_analysis['data']]
            avg_prices = [item['avg_price'] for item in seasonal_analysis['data']]
            avg_sales = [item['avg_sales'] for item in seasonal_analysis['data']]
            
            # 添加价格季节性变化线图
            fig.add_trace(
                go.Scatter(
                    x=periods,
                    y=avg_prices,
                    mode='lines+markers',
                    name="平均价格",
                    line=dict(color='rgb(26, 118, 255)', width=3),
                    marker=dict(size=8)
                ),
                row=1, col=1
            )
            
            # 添加销量季节性变化柱状图
            fig.add_trace(
                go.Bar(
                    x=periods,
                    y=avg_sales,
                    name="平均销量",
                    marker_color='rgb(33, 150, 83)'
                ),
                row=2, col=1
            )
            
            # 更新布局
            period_name = {
                'day': '日',
                'week': '周',
                'month': '月',
                'quarter': '季度'
            }.get(period, period)
            
            title = f"{seasonal_analysis['category_name']}类产品{period_name}度季节性分析" if category_id else f"产品{period_name}度季节性分析"
            
            fig.update_layout(
                title_text=title,
                height=700,
                width=1000,
                showlegend=False
            )
            
            # 更新坐标轴标签
            fig.update_xaxes(title_text=f"{period_name}度", row=1, col=1)
            fig.update_yaxes(title_text="平均价格 (元)", row=1, col=1)
            
            fig.update_xaxes(title_text=f"{period_name}度", row=2, col=1)
            fig.update_yaxes(title_text="平均销量", row=2, col=1)
            
            # 保存图表
            filename = f"seasonal_analysis_{period}_{category_id}" if category_id else f"seasonal_analysis_{period}_all"
            self.save_plotly_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制季节性分析图失败: {e}")
            return None
    
    def plot_competitor_analysis(self, shop_id, limit=10):
        """
        绘制竞争对手分析图
        
        Args:
            shop_id: 店铺ID
            limit: 显示的竞争对手数量
        """
        if not PLOTLY_AVAILABLE:
            logger.error("Plotly库未安装，无法生成交互式竞争对手分析图")
            return None
        
        try:
            # 获取竞争对手分析数据
            competitor_analysis = self.advanced_analyzer.analyze_competitors(shop_id, limit)
            
            if not competitor_analysis['shop_name']:
                logger.error(f"找不到ID为{shop_id}的店铺")
                return None
            
            # 创建子图
            fig = make_subplots(
                rows=2, cols=2,
                specs=[[{"type": "bar"}, {"type": "bar"}],
                       [{"type": "bar"}, {"type": "scatter"}]],
                subplot_titles=("产品数量对比", "平均价格对比", "平均销量对比", "价格与销量关系"),
                vertical_spacing=0.1,
                horizontal_spacing=0.1
            )
            
            # 准备数据
            shop_names = [competitor_analysis['shop_name']] + [item['shop_name'] for item in competitor_analysis['competitors']]
            product_counts = [competitor_analysis['product_count']] + [item['product_count'] for item in competitor_analysis['competitors']]
            avg_prices = [competitor_analysis['avg_price']] + [item['avg_price'] for item in competitor_analysis['competitors']]
            avg_sales = [competitor_analysis['avg_sales']] + [item['avg_sales'] for item in competitor_analysis['competitors']]
            
            # 设置颜色，目标店铺使用不同颜色
            colors = ['rgb(255, 87, 51)'] + ['rgb(55, 83, 109)'] * len(competitor_analysis['competitors'])
            
            # 添加产品数量对比条形图
            fig.add_trace(
                go.Bar(x=shop_names, y=product_counts, name="产品数量", marker_color=colors),
                row=1, col=1
            )
            
            # 添加平均价格对比条形图
            fig.add_trace(
                go.Bar(x=shop_names, y=avg_prices, name="平均价格", marker_color=colors),
                row=1, col=2
            )
            
            # 添加平均销量对比条形图
            fig.add_trace(
                go.Bar(x=shop_names, y=avg_sales, name="平均销量", marker_color=colors),
                row=2, col=1
            )
            
            # 添加价格与销量关系散点图
            fig.add_trace(
                go.Scatter(
                    x=avg_prices,
                    y=avg_sales,
                    mode='markers+text',
                    marker=dict(
                        size=15,
                        color=colors,
                        line=dict(width=2, color='DarkSlateGrey')
                    ),
                    text=shop_names,
                    textposition="top center",
                    name="价格与销量"
                ),
                row=2, col=2
            )
            
            # 更新布局
            fig.update_layout(
                title_text=f"{competitor_analysis['shop_name']}的竞争对手分析",
                height=800,
                width=1200,
                showlegend=False
            )
            
            # 更新坐标轴标签
            fig.update_xaxes(title_text="店铺", row=1, col=1)
            fig.update_yaxes(title_text="产品数量", row=1, col=1)
            
            fig.update_xaxes(title_text="店铺", row=1, col=2)
            fig.update_yaxes(title_text="平均价格 (元)", row=1, col=2)
            
            fig.update_xaxes(title_text="店铺", row=2, col=1)
            fig.update_yaxes(title_text="平均销量", row=2, col=1)
            
            fig.update_xaxes(title_text="平均价格 (元)", row=2, col=2)
            fig.update_yaxes(title_text="平均销量", row=2, col=2)
            
            # 保存图表
            filename = f"competitor_analysis_{shop_id}"
            self.save_plotly_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制竞争对手分析图失败: {e}")
            return None
    
    def plot_keyword_cloud(self, category_id=None, limit=100):
        """
        绘制关键词词云图
        
        Args:
            category_id: 分类ID，如果为None则分析所有分类
            limit: 显示的关键词数量
        """
        if not PLOTLY_AVAILABLE:
            logger.error("Plotly库未安装，无法生成交互式关键词词云图")
            return None
        
        try:
            # 获取关键词统计数据
            keyword_stats = self.text_analyzer.analyze_product_titles(category_id, limit)
            
            # 准备数据
            keywords = [item['keyword'] for item in keyword_stats['keywords']]
            counts = [item['count'] for item in keyword_stats['keywords']]
            
            # 创建词云图
            fig = go.Figure()
            
            # 计算字体大小，基于词频
            max_count = max(counts)
            min_count = min(counts)
            font_sizes = [10 + (count - min_count) * 40 / (max_count - min_count) for count in counts]
            
            # 创建随机位置
            np.random.seed(42)  # 固定随机种子，使结果可重现
            x_pos = np.random.uniform(-1, 1, len(keywords))
            y_pos = np.random.uniform(-1, 1, len(keywords))
            
            # 添加文本
            for i, keyword in enumerate(keywords):
                fig.add_trace(go.Scatter(
                    x=[x_pos[i]],
                    y=[y_pos[i]],
                    mode='text',
                    text=[keyword],
                    textfont=dict(
                        size=font_sizes[i],
                        color=px.colors.qualitative.Plotly[i % len(px.colors.qualitative.Plotly)]
                    ),
                    hoverinfo='text',
                    hovertext=f"{keyword}: {counts[i]}"
                ))
            
            # 更新布局
            title = f"{keyword_stats['category_name']}类产品关键词词云" if category_id else "产品关键词词云"
            
            fig.update_layout(
                title_text=title,
                xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                height=600,
                width=800,
                showlegend=False
            )
            
            # 保存图表
            filename = f"keyword_cloud_{category_id}" if category_id else "keyword_cloud_all"
            self.save_plotly_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制关键词词云图失败: {e}")
            return None
    
    def plot_sentiment_analysis(self, category_id=None):
        """
        绘制情感分析图
        
        Args:
            category_id: 分类ID，如果为None则分析所有分类
        """
        if not PLOTLY_AVAILABLE:
            logger.error("Plotly库未安装，无法生成交互式情感分析图")
            return None
        
        try:
            # 获取情感分析数据
            if category_id:
                sentiment_data = self.sentiment_analyzer.analyze_category_sentiment(category_id)
                categories = [sentiment_data['category_name']]
                positive_rates = [sentiment_data['positive_rate']]
                negative_rates = [sentiment_data['negative_rate']]
                neutral_rates = [sentiment_data['neutral_rate']]
                avg_scores = [sentiment_data['avg_score']]
            else:
                sentiment_data = self.sentiment_analyzer.analyze_all_categories()
                categories = [item['category_name'] for item in sentiment_data]
                positive_rates = [item['positive_rate'] for item in sentiment_data]
                negative_rates = [item['negative_rate'] for item in sentiment_data]
                neutral_rates = [item['neutral_rate'] for item in sentiment_data]
                avg_scores = [item['avg_score'] for item in sentiment_data]
            
            # 创建子图
            fig = make_subplots(
                rows=2, cols=1,
                specs=[[{"type": "bar"}], [{"type": "scatter"}]],
                subplot_titles=("情感分布", "平均情感得分"),
                vertical_spacing=0.2
            )
            
            # 添加情感分布堆叠条形图
            fig.add_trace(
                go.Bar(
                    x=categories,
                    y=positive_rates,
                    name="正面",
                    marker_color='rgb(33, 150, 83)'
                ),
                row=1, col=1
            )
            
            fig.add_trace(
                go.Bar(
                    x=categories,
                    y=neutral_rates,
                    name="中性",
                    marker_color='rgb(151, 151, 151)'
                ),
                row=1, col=1
            )
            
            fig.add_trace(
                go.Bar(
                    x=categories,
                    y=negative_rates,
                    name="负面",
                    marker_color='rgb(255, 87, 51)'
                ),
                row=1, col=1
            )
            
            # 添加平均情感得分散点图
            fig.add_trace(
                go.Scatter(
                    x=categories,
                    y=avg_scores,
                    mode='markers+lines',
                    name="平均情感得分",
                    marker=dict(
                        size=12,
                        color=avg_scores,
                        colorscale='RdYlGn',
                        showscale=True,
                        colorbar=dict(title="情感得分")
                    ),
                    line=dict(width=3)
                ),
                row=2, col=1
            )
            
            # 更新布局
            title = f"{sentiment_data['category_name'] if category_id else '各分类'}产品评论情感分析"
            
            fig.update_layout(
                title_text=title,
                barmode='stack',
                height=800,
                width=1000
            )
            
            # 更新坐标轴标签
            fig.update_xaxes(title_text="分类", row=1, col=1)
            fig.update_yaxes(title_text="比例", row=1, col=1)
            
            fig.update_xaxes(title_text="分类", row=2, col=1)
            fig.update_yaxes(title_text="平均情感得分", row=2, col=1)
            
            # 添加水平参考线（零线）
            fig.add_shape(
                type="line",
                x0=0,
                y0=0,
                x1=1,
                y1=0,
                xref="paper",
                yref="y2",
                line=dict(color="gray", width=2, dash="dash")
            )
            
            # 保存图表
            filename = f"sentiment_analysis_{category_id}" if category_id else "sentiment_analysis_all"
            self.save_plotly_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"绘制情感分析图失败: {e}")
            return None
    
    def create_dashboard(self, category_id=None):
        """
        创建综合仪表盘
        
        Args:
            category_id: 分类ID，如果为None则分析所有分类
        """
        if not PLOTLY_AVAILABLE:
            logger.error("Plotly库未安装，无法生成交互式仪表盘")
            return None
        
        try:
            # 获取基础数据
            category_stats = self.basic_analyzer.get_category_stats()
            price_stats = self.basic_analyzer.get_price_distribution(category_id)
            sales_stats = self.basic_analyzer.get_sales_distribution(category_id)
            location_stats = self.basic_analyzer.get_location_distribution(category_id, limit=10)
            top_products = self.basic_analyzer.get_top_products(category_id, limit=10, by='sales')
            
            # 创建仪表盘
            fig = make_subplots(
                rows=3, cols=2,
                specs=[
                    [{"type": "indicator"}, {"type": "indicator"}],
                    [{"type": "bar"}, {"type": "pie"}],
                    [{"type": "table", "colspan": 2}, None]
                ],
                subplot_titles=(
                    "平均价格", "平均销量",
                    "价格分布", "地区分布",
                    "热门产品排行"
                ),
                vertical_spacing=0.1,
                horizontal_spacing=0.05
            )
            
            # 添加平均价格指示器
            fig.add_trace(
                go.Indicator(
                    mode="number+delta",
                    value=price_stats['avg_price'],
                    number={"prefix": "¥", "valueformat": ".2f"},
                    delta={"position": "bottom", "reference": price_stats['avg_price'] * 0.9, "valueformat": ".2f"},
                    title={"text": "平均价格"}
                ),
                row=1, col=1
            )
            
            # 添加平均销量指示器
            fig.add_trace(
                go.Indicator(
                    mode="number+delta",
                    value=sales_stats['avg_sales'],
                    number={"valueformat": ".1f"},
                    delta={"position": "bottom", "reference": sales_stats['avg_sales'] * 0.9, "valueformat": ".1f"},
                    title={"text": "平均销量"}
                ),
                row=1, col=2
            )
            
            # 添加价格分布条形图
            price_ranges = price_stats['price_ranges']
            ranges = [f"{r['min_price']}-{r['max_price']}" for r in price_ranges]
            counts = [r['count'] for r in price_ranges]
            
            fig.add_trace(
                go.Bar(
                    x=ranges,
                    y=counts,
                    marker_color='rgb(55, 83, 109)'
                ),
                row=2, col=1
            )
            
            # 添加地区分布饼图
            fig.add_trace(
                go.Pie(
                    labels=[item['location'] for item in location_stats],
                    values=[item['count'] for item in location_stats],
                    hole=0.3
                ),
                row=2, col=2
            )
            
            # 添加热门产品表格
            fig.add_trace(
                go.Table(
                    header=dict(
                        values=["排名", "产品名称", "价格", "销量", "店铺"],
                        fill_color='paleturquoise',
                        align='left'
                    ),
                    cells=dict(
                        values=[
                            list(range(1, len(top_products) + 1)),
                            [p['title'] for p in top_products],
                            [f"¥{p['price']:.2f}" for p in top_products],
                            [p['sales'] for p in top_products],
                            [p['shop_name'] for p in top_products]
                        ],
                        fill_color='lavender',
                        align='left'
                    )
                ),
                row=3, col=1
            )
            
            # 更新布局
            title = f"{price_stats['category_name'] if category_id else '所有产品'}数据仪表盘"
            
            fig.update_layout(
                title_text=title,
                height=1000,
                width=1200,
                showlegend=False
            )
            
            # 更新坐标轴标签
            fig.update_xaxes(title_text="价格区间 (元)", row=2, col=1)
            fig.update_yaxes(title_text="产品数量", row=2, col=1)
            
            # 保存仪表盘
            filename = f"dashboard_{category_id}" if category_id else "dashboard_all"
            self.save_plotly_figure(fig, filename)
            
            return fig
        
        except Exception as e:
            logger.error(f"创建仪表盘失败: {e}")
            return None
    
    def generate_all_charts(self, category_id=None):
        """
        生成所有高级图表
        
        Args:
            category_id: 分类ID，如果为None则生成所有产品的图表
        """
        if not PLOTLY_AVAILABLE:
            logger.error("Plotly库未安装，无法生成高级图表")
            return
        
        # 生成价格趋势图
        self.plot_price_trend(category_id)
        
        # 生成销量趋势图
        self.plot_sales_trend(category_id)
        
        # 生成品牌分析图
        self.plot_brand_analysis(category_id)
        
        # 生成季节性分析图
        for period in ['day', 'week', 'month']:
            self.plot_seasonal_analysis(category_id, period)
        
        # 生成关键词词云图
        self.plot_keyword_cloud(category_id)
        
        # 生成情感分析图
        self.plot_sentiment_analysis(category_id)
        
        # 创建仪表盘
        self.create_dashboard(category_id)
        
        logger.info("所有高级图表生成完成")


def main():
    """
    主函数
    """
    import argparse
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='高级数据可视化工具')
    parser.add_argument('--category_id', type=int, help='分类ID')
    parser.add_argument('--output_dir', type=str, default='../output/advanced_charts', help='图表输出目录')
    parser.add_argument('--chart_type', type=str, choices=[
        'price_trend', 'sales_trend', 'brand', 'seasonal',
        'keyword_cloud', 'sentiment', 'dashboard', 'all'
    ], default='all', help='图表类型')
    parser.add_argument('--shop_id', type=int, help='店铺ID，用于竞争对手分析')
    parser.add_argument('--period', type=str, choices=['day', 'week', 'month', 'quarter'], default='month', help='季节性分析周期')
    
    args = parser.parse_args()
    
    # 检查Plotly是否可用
    if not PLOTLY_AVAILABLE:
        logger.error("Plotly库未安装，请先安装Plotly: pip install plotly")
        return
    
    # 创建图表生成器
    charts = AdvancedCharts(output_dir=args.output_dir)
    
    # 根据参数生成相应的图表
    if args.chart_type == 'price_trend':
        charts.plot_price_trend(args.category_id)
    elif args.chart_type == 'sales_trend':
        charts.plot_sales_trend(args.category_id)
    elif args.chart_type == 'brand':
        charts.plot_brand_analysis(args.category_id)
    elif args.chart_type == 'seasonal':
        charts.plot_seasonal_analysis(args.category_id, args.period)
    elif args.chart_type == 'keyword_cloud':
        charts.plot_keyword_cloud(args.category_id)
    elif args.chart_type == 'sentiment':
        charts.plot_sentiment_analysis(args.category_id)
    elif args.chart_type == 'dashboard':
        charts.create_dashboard(args.category_id)
    elif args.shop_id:
        charts.plot_competitor_analysis(args.shop_id)
    else:  # args.chart_type == 'all'
        charts.generate_all_charts(args.category_id)
    
    logger.info("图表生成完成")


if __name__ == "__main__":
    main()