<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}电商数据分析系统{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-chart-line me-2"></i>电商数据分析系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('main.') %}active{% endif %}" href="{{ url_for('main.index') }}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint.startswith('products.') %}active{% endif %}" href="{{ url_for('products.product_list') }}">
                            <i class="fas fa-shopping-cart me-1"></i>产品
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.endpoint.startswith('analysis.') %}active{% endif %}" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar me-1"></i>数据分析
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('analysis.basic_analysis') }}">基础分析</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('analysis.advanced_analysis') }}">高级分析</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('analysis.text_analysis') }}">文本分析</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('analysis.sentiment_analysis') }}">情感分析</a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.endpoint.startswith('visualization.') %}active{% endif %}" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-pie me-1"></i>数据可视化
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('visualization.basic_charts') }}">基础图表</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('visualization.advanced_charts') }}">高级图表</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'main.dashboard' %}active{% endif %}" href="{{ url_for('main.dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>仪表盘
                        </a>
                    </li>
                </ul>
                <form class="d-flex" action="{{ url_for('products.product_search') }}" method="get">
                    <input class="form-control me-2" type="search" name="keyword" placeholder="搜索产品..." aria-label="Search">
                    <button class="btn btn-outline-light" type="submit"><i class="fas fa-search"></i></button>
                </form>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container py-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>电商数据分析系统</h5>
                    <p>一个用于分析电商产品数据的综合平台，提供数据分析和可视化功能。</p>
                </div>
                <div class="col-md-3">
                    <h5>快速链接</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('main.index') }}" class="text-white">首页</a></li>
                        <li><a href="{{ url_for('products.product_list') }}" class="text-white">产品</a></li>
                        <li><a href="{{ url_for('main.dashboard') }}" class="text-white">仪表盘</a></li>
                        <li><a href="{{ url_for('main.about') }}" class="text-white">关于</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>联系我们</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-envelope me-2"></i><EMAIL></li>
                        <li><i class="fas fa-phone me-2"></i>+86 123 4567 8901</li>
                    </ul>
                </div>
            </div>
            <hr>
            <div class="text-center">
                <p>&copy; {{ current_year }} 电商数据分析系统. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>