# -*- coding: utf-8 -*-
"""
数据生成与导入脚本

该脚本用于生成模拟的电商产品数据并将其导入到数据库中
"""

import os
import sys
import json
import logging
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入数据生成器和数据导入模块
from examples.data_generator import generate_and_save, generate_all_categories
from data.data_import import import_data
from crawler.config import ELECTRONIC_CATEGORIES

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("generate_and_import.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def generate_and_import(category_name=None, num_products=50, all_categories=False):
    """
    生成模拟数据并导入到数据库
    
    Args:
        category_name: 分类名称
        num_products: 产品数量
        all_categories: 是否生成所有分类的数据
        
    Returns:
        tuple: (生成的文件路径列表, 导入的产品数量)
    """
    # 生成数据
    if all_categories:
        logger.info(f"开始生成所有分类的数据，每个分类 {num_products} 条")
        file_paths = generate_all_categories(num_products)
        logger.info(f"所有分类数据生成完成，共生成 {len(file_paths)} 个文件")
    else:
        if not category_name:
            category_name = "手机"  # 默认分类
        
        logger.info(f"开始生成分类 {category_name} 的数据，共 {num_products} 条")
        file_path = generate_and_save(category_name, num_products)
        file_paths = [file_path] if file_path else []
        
        if file_path:
            logger.info(f"数据生成完成，保存到 {file_path}")
        else:
            logger.error(f"生成分类 {category_name} 的数据失败")
            return [], 0
    
    # 导入数据到数据库
    total_imported = 0
    for file_path in file_paths:
        if file_path and os.path.exists(file_path):
            # 从文件名中提取分类名称
            filename = os.path.basename(file_path)
            category_name = filename.split('_')[0]
            
            logger.info(f"开始导入文件 {file_path} 到数据库，分类: {category_name}")
            
            try:
                # 导入数据
                imported_count = import_data(file_path, category_name)
                total_imported += imported_count
                
                logger.info(f"成功导入 {imported_count} 条产品数据到数据库")
            except Exception as e:
                logger.error(f"导入数据失败: {str(e)}")
    
    return file_paths, total_imported

def main():
    """
    主函数
    """
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="电商产品数据生成与导入工具")
    parser.add_argument("--category", help="要生成的分类名称")
    parser.add_argument("--num", type=int, default=50, help="生成的产品数量")
    parser.add_argument("--all", action="store_true", help="生成所有分类的数据")
    parser.add_argument("--list", action="store_true", help="列出所有可用的分类")
    
    args = parser.parse_args()
    
    # 列出所有可用的分类
    if args.list:
        print("可用的分类:")
        for category in ELECTRONIC_CATEGORIES:
            print(f"- {category['name']}")
        return
    
    # 生成并导入数据
    start_time = datetime.now()
    
    file_paths, total_imported = generate_and_import(
        category_name=args.category,
        num_products=args.num,
        all_categories=args.all
    )
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    
    # 打印结果
    print("\n数据生成与导入完成!")
    print(f"生成的文件数: {len(file_paths)}")
    print(f"导入的产品数: {total_imported}")
    print(f"总耗时: {duration:.2f} 秒")
    
    # 提示后续步骤
    print("\n后续步骤:")
    print("1. 运行数据分析: python -m analysis.analyze_data")
    print("2. 启动Web仪表盘: python -m dashboard.app")

if __name__ == "__main__":
    main()