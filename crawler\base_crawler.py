# -*- coding: utf-8 -*-
"""
爬虫基类，定义爬虫的通用方法和属性
"""

import os
import time
import random
import logging
from abc import ABC, abstractmethod
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("crawler.log"),
        logging.StreamHandler()
    ]
)


class BaseCrawler(ABC):
    """
    爬虫基类，提供浏览器初始化、页面访问等基础功能
    """

    def __init__(self):
        """
        初始化爬虫，配置Selenium WebDriver
        """
        self.logger = logging.getLogger(self.__class__.__name__)
        self.timeout = int(os.getenv('TIMEOUT', 30))
        self.max_retries = int(os.getenv('MAX_RETRIES', 3))
        self.delay_min = int(os.getenv('DELAY_MIN', 2))
        self.delay_max = int(os.getenv('DELAY_MAX', 5))
        self.headless = os.getenv('HEADLESS', 'False').lower() == 'true'
        self.driver = None
        self.init_driver()

    def init_driver(self):
        """
        初始化Edge WebDriver
        """
        try:
            edge_options = Options()

            # 设置无头模式
            if self.headless:
                edge_options.add_argument('--headless')

            # 添加反检测选项
            edge_options.add_argument('--disable-blink-features=AutomationControlled')
            edge_options.add_argument('--disable-infobars')
            edge_options.add_argument('--disable-dev-shm-usage')
            edge_options.add_argument('--no-sandbox')
            edge_options.add_argument('--disable-gpu')
            edge_options.add_experimental_option('excludeSwitches', ['enable-automation'])
            edge_options.add_experimental_option('useAutomationExtension', False)

            # 设置用户代理
            edge_options.add_argument(f'user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) '
                                      f'AppleWebKit/537.36 (KHTML, like Gecko) '
                                      f'Chrome/{random.randint(90, 110)}.0.{random.randint(1000, 5000)}.'
                                      f'{random.randint(10, 100)} Safari/537.36')

            # 初始化WebDriver
            # 使用本地的msedgedriver.exe文件
            service = Service("./crawler/msedgedriver.exe")
            self.driver = webdriver.Edge(service=service, options=edge_options)

            # 设置窗口大小
            # self.driver.set_window_size(1920, 1080)
            # 窗口最大化
            self.driver.maximize_window()

            # 设置页面加载超时时间
            self.driver.set_page_load_timeout(self.timeout)
            self.driver.set_script_timeout(self.timeout)

            # 执行反检测JavaScript
            self.execute_stealth_js()

            self.logger.info("WebDriver初始化成功")
        except Exception as e:
            self.logger.error(f"WebDriver初始化失败: {str(e)}")
            raise

    def execute_stealth_js(self):
        """
        执行反检测JavaScript
        """
        # 修改WebDriver属性，绕过检测
        stealth_js = """
        Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
        window.navigator.chrome = {runtime: {}};
        Object.defineProperty(navigator, 'languages', {get: () => ['zh-CN', 'zh', 'en']});
        Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
        """
        try:
            self.driver.execute_script(stealth_js)
        except Exception as e:
            self.logger.warning(f"执行反检测JS失败: {str(e)}")

    def visit_url(self, url, retry=0):
        """
        访问指定URL，带有重试机制

        Args:
            url: 要访问的URL
            retry: 当前重试次数

        Returns:
            bool: 是否访问成功
        """
        try:
            self.logger.info(f"正在访问: {url}")
            self.driver.get(url)
            self.random_sleep()
            return True
        except (TimeoutException, WebDriverException) as e:
            self.logger.warning(f"访问 {url} 失败: {str(e)}")
            if retry < self.max_retries:
                retry += 1
                self.logger.info(f"第 {retry} 次重试访问 {url}")
                # 重试前等待更长时间
                time.sleep(retry * 2)
                return self.visit_url(url, retry)
            else:
                self.logger.error(f"访问 {url} 失败，已达到最大重试次数")
                return False

    def random_sleep(self, min_time=None, max_time=None):
        """
        随机等待一段时间，模拟人类行为

        Args:
            min_time: 最小等待时间（秒）
            max_time: 最大等待时间（秒）
        """
        min_time = min_time if min_time is not None else self.delay_min
        max_time = max_time if max_time is not None else self.delay_max
        sleep_time = random.uniform(min_time, max_time)
        self.logger.debug(f"随机等待 {sleep_time:.2f} 秒")
        time.sleep(sleep_time)

    def close(self):
        """
        关闭WebDriver
        """
        if self.driver:
            self.driver.quit()
            self.logger.info("WebDriver已关闭")

    def __del__(self):
        """
        析构函数，确保WebDriver被关闭
        """
        self.close()

    @abstractmethod
    def crawl(self, *args, **kwargs):
        """
        爬取数据的抽象方法，子类必须实现
        """
        pass
