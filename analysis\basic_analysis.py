# -*- coding: utf-8 -*-
"""
基础数据分析模块，实现对电子产品数据的基本统计分析
"""

import pandas as pd
import numpy as np
import logging
from sqlalchemy import func, desc

from data.db_connection import get_db_session, close_db_session
from data.models import Product, Category, Shop, PriceHistory, SalesHistory

# 配置日志
logger = logging.getLogger(__name__)

class BasicAnalyzer:
    """
    基础数据分析类，提供基本的统计分析功能
    """
    
    def __init__(self):
        """
        初始化分析器
        """
        self.session = get_db_session()
    
    def close(self):
        """
        关闭数据库会话
        """
        close_db_session()
    
    def get_category_statistics(self):
        """
        获取各分类的统计信息
        
        Returns:
            DataFrame: 分类统计信息
        """
        try:
            # 查询各分类的产品数量、平均价格、平均销量
            query = self.session.query(
                Category.name.label('category_name'),
                func.count(Product.id).label('product_count'),
                func.avg(Product.price).label('avg_price'),
                func.avg(Product.sales).label('avg_sales'),
                func.min(Product.price).label('min_price'),
                func.max(Product.price).label('max_price'),
                func.sum(Product.sales).label('total_sales')
            ).join(Product, Category.id == Product.category_id)\
            .group_by(Category.name)\
            .order_by(desc('product_count'))
            
            # 转换为DataFrame
            df = pd.read_sql(query.statement, self.session.bind)
            
            # 处理NaN值
            df = df.fillna(0)
            
            # 格式化数值
            df['avg_price'] = df['avg_price'].round(2)
            df['avg_sales'] = df['avg_sales'].round(2)
            
            return df
        except Exception as e:
            logger.error(f"获取分类统计信息失败: {str(e)}")
            return pd.DataFrame()
    
    def get_price_distribution(self, category_name=None):
        """
        获取价格分布
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            
        Returns:
            DataFrame: 价格分布
        """
        try:
            # 构建查询
            query = self.session.query(Product.price)
            
            # 如果指定了分类，则筛选该分类的产品
            if category_name:
                query = query.join(Category, Product.category_id == Category.id)\
                    .filter(Category.name == category_name)
            
            # 执行查询
            prices = [p[0] for p in query.all() if p[0] is not None]
            
            if not prices:
                logger.warning(f"未找到{'分类 ' + category_name if category_name else '任何'}的价格数据")
                return pd.DataFrame()
            
            # 创建价格区间
            price_ranges = [
                (0, 500), (500, 1000), (1000, 2000), (2000, 3000),
                (3000, 5000), (5000, 8000), (8000, 10000), (10000, float('inf'))
            ]
            
            # 统计各价格区间的产品数量
            distribution = []
            for start, end in price_ranges:
                count = sum(1 for p in prices if start <= p < end)
                percentage = count / len(prices) * 100 if prices else 0
                distribution.append({
                    'price_range': f"{start}-{end if end != float('inf') else '以上'}",
                    'count': count,
                    'percentage': round(percentage, 2)
                })
            
            return pd.DataFrame(distribution)
        except Exception as e:
            logger.error(f"获取价格分布失败: {str(e)}")
            return pd.DataFrame()
    
    def get_sales_distribution(self, category_name=None):
        """
        获取销量分布
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            
        Returns:
            DataFrame: 销量分布
        """
        try:
            # 构建查询
            query = self.session.query(Product.sales)
            
            # 如果指定了分类，则筛选该分类的产品
            if category_name:
                query = query.join(Category, Product.category_id == Category.id)\
                    .filter(Category.name == category_name)
            
            # 执行查询
            sales = [s[0] for s in query.all() if s[0] is not None]
            
            if not sales:
                logger.warning(f"未找到{'分类 ' + category_name if category_name else '任何'}的销量数据")
                return pd.DataFrame()
            
            # 创建销量区间
            sales_ranges = [
                (0, 10), (10, 50), (50, 100), (100, 500),
                (500, 1000), (1000, 5000), (5000, 10000), (10000, float('inf'))
            ]
            
            # 统计各销量区间的产品数量
            distribution = []
            for start, end in sales_ranges:
                count = sum(1 for s in sales if start <= s < end)
                percentage = count / len(sales) * 100 if sales else 0
                distribution.append({
                    'sales_range': f"{start}-{end if end != float('inf') else '以上'}",
                    'count': count,
                    'percentage': round(percentage, 2)
                })
            
            return pd.DataFrame(distribution)
        except Exception as e:
            logger.error(f"获取销量分布失败: {str(e)}")
            return pd.DataFrame()
    
    def get_top_products(self, category_name=None, by='sales', limit=10):
        """
        获取热门产品
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            by: 排序依据，'sales'表示按销量排序，'price'表示按价格排序
            limit: 返回的产品数量
            
        Returns:
            DataFrame: 热门产品
        """
        try:
            # 构建查询
            query = self.session.query(
                Product.title,
                Product.price,
                Product.sales,
                Product.rating,
                Category.name.label('category_name'),
                Shop.name.label('shop_name')
            ).join(Category, Product.category_id == Category.id)\
            .join(Shop, Product.shop_id == Shop.id, isouter=True)
            
            # 如果指定了分类，则筛选该分类的产品
            if category_name:
                query = query.filter(Category.name == category_name)
            
            # 按指定字段排序
            if by == 'sales':
                query = query.order_by(desc(Product.sales))
            elif by == 'price':
                query = query.order_by(desc(Product.price))
            
            # 限制返回数量
            query = query.limit(limit)
            
            # 转换为DataFrame
            df = pd.read_sql(query.statement, self.session.bind)
            
            # 处理NaN值
            df = df.fillna({'shop_name': '未知', 'rating': 0})
            
            return df
        except Exception as e:
            logger.error(f"获取热门产品失败: {str(e)}")
            return pd.DataFrame()
    
    def get_top_shops(self, category_name=None, limit=10):
        """
        获取热门店铺
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            limit: 返回的店铺数量
            
        Returns:
            DataFrame: 热门店铺
        """
        try:
            # 构建查询
            query = self.session.query(
                Shop.name.label('shop_name'),
                func.count(Product.id).label('product_count'),
                func.avg(Product.price).label('avg_price'),
                func.avg(Product.sales).label('avg_sales'),
                func.sum(Product.sales).label('total_sales')
            ).join(Product, Shop.id == Product.shop_id)
            
            # 如果指定了分类，则筛选该分类的产品
            if category_name:
                query = query.join(Category, Product.category_id == Category.id)\
                    .filter(Category.name == category_name)
            
            # 分组并排序
            query = query.group_by(Shop.name)\
                .order_by(desc('product_count'))\
                .limit(limit)
            
            # 转换为DataFrame
            df = pd.read_sql(query.statement, self.session.bind)
            
            # 处理NaN值
            df = df.fillna(0)
            
            # 格式化数值
            df['avg_price'] = df['avg_price'].round(2)
            df['avg_sales'] = df['avg_sales'].round(2)
            
            return df
        except Exception as e:
            logger.error(f"获取热门店铺失败: {str(e)}")
            return pd.DataFrame()
    
    def get_location_distribution(self, category_name=None):
        """
        获取地区分布
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            
        Returns:
            DataFrame: 地区分布
        """
        try:
            # 构建查询
            query = self.session.query(
                Product.location,
                func.count(Product.id).label('product_count'),
                func.avg(Product.price).label('avg_price'),
                func.avg(Product.sales).label('avg_sales')
            )
            
            # 如果指定了分类，则筛选该分类的产品
            if category_name:
                query = query.join(Category, Product.category_id == Category.id)\
                    .filter(Category.name == category_name)
            
            # 分组并排序
            query = query.group_by(Product.location)\
                .order_by(desc('product_count'))
            
            # 转换为DataFrame
            df = pd.read_sql(query.statement, self.session.bind)
            
            # 处理NaN值和空字符串
            df['location'] = df['location'].fillna('未知')
            df.loc[df['location'] == '', 'location'] = '未知'
            
            # 提取省份信息
            df['province'] = df['location'].apply(lambda x: x.split(' ')[0] if ' ' in x else x)
            
            # 按省份分组
            province_df = df.groupby('province').agg({
                'product_count': 'sum',
                'avg_price': 'mean',
                'avg_sales': 'mean'
            }).reset_index()
            
            # 格式化数值
            province_df['avg_price'] = province_df['avg_price'].round(2)
            province_df['avg_sales'] = province_df['avg_sales'].round(2)
            
            # 按产品数量排序
            province_df = province_df.sort_values('product_count', ascending=False)
            
            return province_df
        except Exception as e:
            logger.error(f"获取地区分布失败: {str(e)}")
            return pd.DataFrame()
    
    def get_keyword_statistics(self, category_name=None):
        """
        获取关键词统计
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            
        Returns:
            DataFrame: 关键词统计
        """
        try:
            # 构建查询
            query = self.session.query(
                Product.keyword,
                func.count(Product.id).label('product_count'),
                func.avg(Product.price).label('avg_price'),
                func.avg(Product.sales).label('avg_sales'),
                func.sum(Product.sales).label('total_sales')
            )
            
            # 如果指定了分类，则筛选该分类的产品
            if category_name:
                query = query.join(Category, Product.category_id == Category.id)\
                    .filter(Category.name == category_name)
            
            # 分组并排序
            query = query.group_by(Product.keyword)\
                .order_by(desc('product_count'))
            
            # 转换为DataFrame
            df = pd.read_sql(query.statement, self.session.bind)
            
            # 处理NaN值和空字符串
            df['keyword'] = df['keyword'].fillna('未知')
            df.loc[df['keyword'] == '', 'keyword'] = '未知'
            
            # 格式化数值
            df['avg_price'] = df['avg_price'].round(2)
            df['avg_sales'] = df['avg_sales'].round(2)
            
            return df
        except Exception as e:
            logger.error(f"获取关键词统计失败: {str(e)}")
            return pd.DataFrame()
    
    def get_price_sales_correlation(self, category_name=None):
        """
        获取价格与销量的相关性
        
        Args:
            category_name: 分类名称，如果为None则分析所有产品
            
        Returns:
            dict: 相关性分析结果
        """
        try:
            # 构建查询
            query = self.session.query(Product.price, Product.sales)
            
            # 如果指定了分类，则筛选该分类的产品
            if category_name:
                query = query.join(Category, Product.category_id == Category.id)\
                    .filter(Category.name == category_name)
            
            # 执行查询
            result = query.all()
            
            # 转换为DataFrame
            df = pd.DataFrame(result, columns=['price', 'sales'])
            
            # 过滤掉缺失值
            df = df.dropna()
            
            if df.empty:
                logger.warning(f"未找到{'分类 ' + category_name if category_name else '任何'}的价格和销量数据")
                return {'correlation': 0, 'sample_size': 0}
            
            # 计算相关系数
            correlation = df['price'].corr(df['sales'])
            
            return {
                'correlation': round(correlation, 4),
                'sample_size': len(df)
            }
        except Exception as e:
            logger.error(f"获取价格与销量的相关性失败: {str(e)}")
            return {'correlation': 0, 'sample_size': 0}

# 导出分析函数
def analyze_data(category_name=None):
    """
    分析数据的主函数
    
    Args:
        category_name: 分类名称，如果为None则分析所有产品
        
    Returns:
        dict: 分析结果
    """
    analyzer = BasicAnalyzer()
    try:
        # 执行各种分析
        category_stats = analyzer.get_category_statistics()
        price_distribution = analyzer.get_price_distribution(category_name)
        sales_distribution = analyzer.get_sales_distribution(category_name)
        top_products_by_sales = analyzer.get_top_products(category_name, by='sales')
        top_products_by_price = analyzer.get_top_products(category_name, by='price')
        top_shops = analyzer.get_top_shops(category_name)
        location_distribution = analyzer.get_location_distribution(category_name)
        keyword_statistics = analyzer.get_keyword_statistics(category_name)
        price_sales_correlation = analyzer.get_price_sales_correlation(category_name)
        
        # 返回分析结果
        return {
            'category_statistics': category_stats.to_dict('records') if not category_stats.empty else [],
            'price_distribution': price_distribution.to_dict('records') if not price_distribution.empty else [],
            'sales_distribution': sales_distribution.to_dict('records') if not sales_distribution.empty else [],
            'top_products_by_sales': top_products_by_sales.to_dict('records') if not top_products_by_sales.empty else [],
            'top_products_by_price': top_products_by_price.to_dict('records') if not top_products_by_price.empty else [],
            'top_shops': top_shops.to_dict('records') if not top_shops.empty else [],
            'location_distribution': location_distribution.to_dict('records') if not location_distribution.empty else [],
            'keyword_statistics': keyword_statistics.to_dict('records') if not keyword_statistics.empty else [],
            'price_sales_correlation': price_sales_correlation
        }
    finally:
        analyzer.close()

# 命令行入口
if __name__ == "__main__":
    import argparse
    import json
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="淘宝电子产品数据分析工具")
    parser.add_argument("--category", help="分类名称，如果不指定则分析所有产品")
    parser.add_argument("--output", help="输出文件路径，如果不指定则打印到控制台")
    
    args = parser.parse_args()
    
    # 分析数据
    result = analyze_data(args.category)
    
    # 输出结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"分析结果已保存到 {args.output}")
    else:
        print(json.dumps(result, ensure_ascii=False, indent=2))