# -*- coding: utf-8 -*-
"""
启动Web仪表盘的快捷脚本
"""

import os
import sys
import logging
import argparse
from dotenv import load_dotenv

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("dashboard.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def main():
    """
    主函数
    """
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="启动电商数据分析Web仪表盘")
    parser.add_argument("--host", default="127.0.0.1", help="主机地址")
    parser.add_argument("--port", type=int, default=5000, help="端口号")
    parser.add_argument("--debug", action="store_true", help="是否开启调试模式")
    
    args = parser.parse_args()
    
    # 加载环境变量
    env_file = os.path.join(os.path.dirname(__file__), ".env")
    if os.path.exists(env_file):
        load_dotenv(env_file)
        logger.info("已加载环境变量")
    else:
        logger.warning(".env文件不存在，使用默认配置")
    
    try:
        # 导入Web仪表盘模块
        from examples.web_dashboard import app
        
        # 检查模板目录是否存在
        template_dir = os.path.join(os.path.dirname(__file__), "templates")
        if not os.path.exists(template_dir):
            logger.warning(f"模板目录不存在: {template_dir}")
            logger.info("尝试创建模板目录并生成默认模板")
            
            # 创建模板目录
            os.makedirs(template_dir, exist_ok=True)
            
            # 导入并调用创建模板的函数
            from examples.web_dashboard import create_dashboard_template
            create_dashboard_template(template_dir)
        
        # 检查可视化目录是否存在
        vis_dir = os.path.join(os.path.dirname(__file__), "data/visualization")
        if not os.path.exists(vis_dir):
            os.makedirs(vis_dir, exist_ok=True)
            logger.info(f"已创建可视化目录: {vis_dir}")
        
        # 启动Web仪表盘
        logger.info(f"启动Web仪表盘，地址: {args.host}:{args.port}")
        print(f"\n启动Web仪表盘，地址: http://{args.host}:{args.port}")
        print("按Ctrl+C停止服务\n")
        
        app.run(host=args.host, port=args.port, debug=args.debug)
        
    except ImportError as e:
        logger.error(f"导入Web仪表盘模块失败: {str(e)}")
        print("\n错误: 导入Web仪表盘模块失败")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
        print("并且已运行项目初始化脚本: python setup.py")
        sys.exit(1)
    except Exception as e:
        logger.error(f"启动Web仪表盘时发生错误: {str(e)}")
        print(f"\n错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n用户中断，停止Web仪表盘")
        logger.info("用户中断，停止Web仪表盘")
        sys.exit(0)