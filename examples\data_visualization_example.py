# -*- coding: utf-8 -*-
"""
数据可视化示例脚本

该脚本展示如何使用数据可视化模块生成电商数据的可视化图表
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入数据可视化模块
from data.data_visualization import get_visualizer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("data_visualization.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def visualize_price_distribution(category_name=None):
    """
    可视化价格分布
    
    Args:
        category_name: 分类名称，如果为None则获取所有分类
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/visualization")
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取可视化器
    visualizer = get_visualizer(output_dir)
    
    # 生成价格分布图
    filename = visualizer.generate_price_distribution_chart(category_name)
    
    if filename:
        logger.info(f"价格分布图已生成: {filename}")
        print(f"价格分布图已保存到: {os.path.join(output_dir, filename)}")
    else:
        logger.warning("价格分布图生成失败")

def visualize_price_trend(category_name=None, days=30):
    """
    可视化价格趋势
    
    Args:
        category_name: 分类名称，如果为None则获取所有分类
        days: 天数
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/visualization")
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取可视化器
    visualizer = get_visualizer(output_dir)
    
    # 生成价格趋势图
    filename = visualizer.generate_price_trend_chart(category_name, days)
    
    if filename:
        logger.info(f"价格趋势图已生成: {filename}")
        print(f"价格趋势图已保存到: {os.path.join(output_dir, filename)}")
    else:
        logger.warning("价格趋势图生成失败")

def visualize_category_stats():
    """
    可视化分类统计
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/visualization")
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取可视化器
    visualizer = get_visualizer(output_dir)
    
    # 生成分类统计图
    count_filename, price_filename = visualizer.generate_category_stats_chart()
    
    if count_filename and price_filename:
        logger.info(f"分类产品数量图已生成: {count_filename}")
        logger.info(f"分类平均价格图已生成: {price_filename}")
        print(f"分类产品数量图已保存到: {os.path.join(output_dir, count_filename)}")
        print(f"分类平均价格图已保存到: {os.path.join(output_dir, price_filename)}")
    else:
        logger.warning("分类统计图生成失败")

def visualize_sales_trend(category_name=None, days=30):
    """
    可视化销量趋势
    
    Args:
        category_name: 分类名称，如果为None则获取所有分类
        days: 天数
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/visualization")
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取可视化器
    visualizer = get_visualizer(output_dir)
    
    # 生成销量趋势图
    filename = visualizer.generate_sales_trend_chart(category_name, days)
    
    if filename:
        logger.info(f"销量趋势图已生成: {filename}")
        print(f"销量趋势图已保存到: {os.path.join(output_dir, filename)}")
    else:
        logger.warning("销量趋势图生成失败")

def visualize_shop_stats(limit=10):
    """
    可视化店铺统计
    
    Args:
        limit: 返回的店铺数量
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/visualization")
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取可视化器
    visualizer = get_visualizer(output_dir)
    
    # 生成店铺统计图
    filename = visualizer.generate_shop_stats_chart(limit)
    
    if filename:
        logger.info(f"店铺统计图已生成: {filename}")
        print(f"店铺统计图已保存到: {os.path.join(output_dir, filename)}")
    else:
        logger.warning("店铺统计图生成失败")

def visualize_location_stats(limit=10):
    """
    可视化地区统计
    
    Args:
        limit: 返回的地区数量
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/visualization")
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取可视化器
    visualizer = get_visualizer(output_dir)
    
    # 生成地区统计图
    filename = visualizer.generate_location_stats_chart(limit)
    
    if filename:
        logger.info(f"地区统计图已生成: {filename}")
        print(f"地区统计图已保存到: {os.path.join(output_dir, filename)}")
    else:
        logger.warning("地区统计图生成失败")

def visualize_price_sales_scatter(category_name=None, limit=100):
    """
    可视化价格-销量散点图
    
    Args:
        category_name: 分类名称，如果为None则获取所有分类
        limit: 产品数量限制
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/visualization")
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取可视化器
    visualizer = get_visualizer(output_dir)
    
    # 生成价格-销量散点图
    filename = visualizer.generate_price_sales_scatter_chart(category_name, limit)
    
    if filename:
        logger.info(f"价格-销量散点图已生成: {filename}")
        print(f"价格-销量散点图已保存到: {os.path.join(output_dir, filename)}")
    else:
        logger.warning("价格-销量散点图生成失败")

def visualize_dashboard(category_name=None, days=30):
    """
    生成仪表盘所需的所有图表
    
    Args:
        category_name: 分类名称，如果为None则获取所有分类
        days: 天数
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/visualization/dashboard")
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取可视化器
    visualizer = get_visualizer(output_dir)
    
    # 生成仪表盘图表
    charts = visualizer.generate_dashboard_charts(category_name, days)
    
    if charts:
        logger.info(f"仪表盘图表已生成: {', '.join(charts.values())}")
        print("仪表盘图表已生成:")
        for chart_name, filename in charts.items():
            if filename:
                print(f"- {chart_name}: {os.path.join(output_dir, filename)}")
    else:
        logger.warning("仪表盘图表生成失败")

def main():
    """
    主函数
    """
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="淘宝电子产品数据可视化工具")
    parser.add_argument("--category", help="要分析的分类名称")
    parser.add_argument("--days", type=int, default=30, help="趋势分析的天数")
    parser.add_argument("--limit", type=int, default=10, help="返回的结果数量")
    parser.add_argument("--all", action="store_true", help="生成所有可视化图表")
    parser.add_argument("--price-distribution", action="store_true", help="生成价格分布图")
    parser.add_argument("--price-trend", action="store_true", help="生成价格趋势图")
    parser.add_argument("--category-stats", action="store_true", help="生成分类统计图")
    parser.add_argument("--sales-trend", action="store_true", help="生成销量趋势图")
    parser.add_argument("--shop-stats", action="store_true", help="生成店铺统计图")
    parser.add_argument("--location-stats", action="store_true", help="生成地区统计图")
    parser.add_argument("--price-sales-scatter", action="store_true", help="生成价格-销量散点图")
    parser.add_argument("--dashboard", action="store_true", help="生成仪表盘图表")
    
    args = parser.parse_args()
    
    # 如果没有指定任何可视化，则默认生成仪表盘图表
    if not (args.all or args.price_distribution or args.price_trend or 
            args.category_stats or args.sales_trend or args.shop_stats or 
            args.location_stats or args.price_sales_scatter or args.dashboard):
        args.dashboard = True
    
    try:
        # 生成指定的可视化图表
        if args.all or args.price_distribution:
            visualize_price_distribution(args.category)
        
        if args.all or args.price_trend:
            visualize_price_trend(args.category, args.days)
        
        if args.all or args.category_stats:
            visualize_category_stats()
        
        if args.all or args.sales_trend:
            visualize_sales_trend(args.category, args.days)
        
        if args.all or args.shop_stats:
            visualize_shop_stats(args.limit)
        
        if args.all or args.location_stats:
            visualize_location_stats(args.limit)
        
        if args.all or args.price_sales_scatter:
            visualize_price_sales_scatter(args.category, args.limit * 10)
        
        if args.all or args.dashboard:
            visualize_dashboard(args.category, args.days)
        
        logger.info("数据可视化完成")
        
    except Exception as e:
        logger.error(f"数据可视化过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    main()