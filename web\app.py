# -*- coding: utf-8 -*-
"""
Flask应用主文件，用于初始化和配置Flask应用
"""

import os
import logging
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_caching import Cache
from flask_wtf.csrf import CSRFProtect

from web.config import config

# 初始化扩展，避免循环导入
db = SQLAlchemy()
migrate = Migrate()
cache = Cache()
csrf = CSRFProtect()


def create_app(config_name=None):
    """
    创建Flask应用实例
    
    Args:
        config_name: 配置名称，可选值为'development', 'testing', 'production'
    
    Returns:
        Flask应用实例
    """
    # 如果未指定配置，则从环境变量获取，默认为开发环境
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    # 创建应用实例
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config[config_name])
    
    # 确保上传目录存在
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['CHARTS_FOLDER'], exist_ok=True)
    
    # 配置日志
    configure_logging(app)
    
    # 初始化扩展
    initialize_extensions(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 注册上下文处理器
    register_context_processors(app)
    
    # 注册命令
    register_commands(app)
    
    return app


def configure_logging(app):
    """
    配置日志
    
    Args:
        app: Flask应用实例
    """
    log_level = logging.DEBUG if app.config['DEBUG'] else logging.INFO
    
    # 配置根日志记录器
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 配置Flask应用日志记录器
    app.logger.setLevel(log_level)


def initialize_extensions(app):
    """
    初始化Flask扩展
    
    Args:
        app: Flask应用实例
    """
    # 初始化SQLAlchemy
    db.init_app(app)
    
    # 初始化Flask-Migrate
    migrate.init_app(app, db)
    
    # 初始化Flask-Caching
    cache.init_app(app)
    
    # 初始化CSRF保护
    csrf.init_app(app)


def register_blueprints(app):
    """
    注册蓝图
    
    Args:
        app: Flask应用实例
    """
    # 导入蓝图
    from web.views.main import main_bp
    from web.views.products import products_bp
    from web.views.analysis import analysis_bp
    from web.views.visualization import visualization_bp
    from web.views.api import api_bp
    
    # 注册蓝图
    app.register_blueprint(main_bp)
    app.register_blueprint(products_bp, url_prefix='/products')
    app.register_blueprint(analysis_bp, url_prefix='/analysis')
    app.register_blueprint(visualization_bp, url_prefix='/visualization')
    app.register_blueprint(api_bp, url_prefix='/api')


def register_error_handlers(app):
    """
    注册错误处理器
    
    Args:
        app: Flask应用实例
    """
    @app.errorhandler(404)
    def page_not_found(e):
        from flask import render_template
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_server_error(e):
        from flask import render_template
        return render_template('errors/500.html'), 500


def register_context_processors(app):
    """
    注册上下文处理器
    
    Args:
        app: Flask应用实例
    """
    @app.context_processor
    def inject_app_name():
        return {'app_name': app.config['APP_NAME']}


def register_commands(app):
    """
    注册自定义命令
    
    Args:
        app: Flask应用实例
    """
    @app.cli.command('init-db')
    def init_db_command():
        """初始化数据库"""
        from data.init_db import init_database
        init_database()
        click.echo('数据库初始化完成。')
    
    @app.cli.command('import-data')
    @click.argument('data_dir', type=click.Path(exists=True))
    def import_data_command(data_dir):
        """导入数据"""
        from data.data_import import DataImporter
        importer = DataImporter()
        importer.import_from_directory(data_dir)
        click.echo('数据导入完成。')


# 导入视图模块，确保视图被注册
from web.views import main, products, analysis, visualization, api