# -*- coding: utf-8 -*-
"""
分析视图模块，用于展示数据分析结果
"""

from flask import Blueprint, render_template, request, jsonify, current_app
from sqlalchemy import func, desc
import pandas as pd
import numpy as np

from web.app import db, cache
from data.models import Product, Category, Shop, PriceHistory, SalesHistory
from analysis.basic_analysis import BasicAnalyzer
from analysis.advanced_analysis import AdvancedAnalyzer
from analysis.text_analysis import TextAnalyzer
from analysis.sentiment_analysis import SentimentAnalyzer

# 创建蓝图
analysis_bp = Blueprint('analysis', __name__)


@analysis_bp.route('/')
def index():
    """
    分析首页
    显示可用的分析类型
    """
    return render_template('analysis/index.html')


@analysis_bp.route('/basic')
@cache.cached(timeout=600)  # 缓存10分钟
def basic_analysis():
    """
    基础分析页面
    显示基本统计分析结果
    """
    # 创建基础分析器
    analyzer = BasicAnalyzer()
    
    # 获取分类统计
    category_stats = analyzer.get_category_stats()
    
    # 获取价格分布
    price_distribution = analyzer.get_price_distribution()
    
    # 获取销量分布
    sales_distribution = analyzer.get_sales_distribution()
    
    # 获取热门产品
    top_products = analyzer.get_top_products(limit=10)
    
    # 获取热门店铺
    top_shops = analyzer.get_top_shops(limit=10)
    
    # 获取地区分布
    location_distribution = analyzer.get_location_distribution()
    
    # 获取价格与销量的相关性
    price_sales_correlation = analyzer.get_price_sales_correlation()
    
    return render_template(
        'analysis/basic.html',
        category_stats=category_stats,
        price_distribution=price_distribution,
        sales_distribution=sales_distribution,
        top_products=top_products,
        top_shops=top_shops,
        location_distribution=location_distribution,
        price_sales_correlation=price_sales_correlation
    )


@analysis_bp.route('/advanced')
@cache.cached(timeout=600)  # 缓存10分钟
def advanced_analysis():
    """
    高级分析页面
    显示高级统计分析结果
    """
    # 创建高级分析器
    analyzer = AdvancedAnalyzer()
    
    # 获取时间序列分析结果
    time_series_data = analyzer.analyze_time_series()
    
    # 获取品牌分析结果
    brand_analysis = analyzer.analyze_brands()
    
    # 获取价格趋势分析结果
    price_trend = analyzer.analyze_price_trends()
    
    # 获取季节性分析结果
    seasonality = analyzer.analyze_seasonality()
    
    # 获取竞争分析结果
    competition_analysis = analyzer.analyze_competition()
    
    return render_template(
        'analysis/advanced.html',
        time_series_data=time_series_data,
        brand_analysis=brand_analysis,
        price_trend=price_trend,
        seasonality=seasonality,
        competition_analysis=competition_analysis
    )


@analysis_bp.route('/text')
@cache.cached(timeout=600)  # 缓存10分钟
def text_analysis():
    """
    文本分析页面
    显示文本分析结果
    """
    # 创建文本分析器
    analyzer = TextAnalyzer()
    
    # 获取分类ID参数
    category_id = request.args.get('category_id', None, type=int)
    
    # 获取所有分类
    categories = Category.query.all()
    
    # 如果指定了分类，则分析该分类的产品标题
    if category_id:
        category = Category.query.get_or_404(category_id)
        title_analysis = analyzer.analyze_product_titles(category_id=category_id)
    else:
        category = None
        # 分析所有产品标题
        title_analysis = analyzer.analyze_product_titles()
    
    # 获取关键词统计
    keyword_stats = analyzer.get_keyword_stats(category_id=category_id)
    
    # 获取品牌统计
    brand_stats = analyzer.get_brand_stats(category_id=category_id)
    
    return render_template(
        'analysis/text.html',
        categories=categories,
        current_category=category,
        title_analysis=title_analysis,
        keyword_stats=keyword_stats,
        brand_stats=brand_stats
    )


@analysis_bp.route('/sentiment')
@cache.cached(timeout=600)  # 缓存10分钟
def sentiment_analysis():
    """
    情感分析页面
    显示情感分析结果
    """
    # 创建情感分析器
    analyzer = SentimentAnalyzer()
    
    # 获取分类ID参数
    category_id = request.args.get('category_id', None, type=int)
    
    # 获取所有分类
    categories = Category.query.all()
    
    # 如果指定了分类，则分析该分类的产品评论
    if category_id:
        category = Category.query.get_or_404(category_id)
        sentiment_results = analyzer.analyze_category_sentiment(category_id)
    else:
        category = None
        # 分析所有分类的情感
        sentiment_results = analyzer.analyze_all_categories()
    
    return render_template(
        'analysis/sentiment.html',
        categories=categories,
        current_category=category,
        sentiment_results=sentiment_results
    )


@analysis_bp.route('/api/category_stats')
def api_category_stats():
    """
    分类统计API
    用于AJAX请求获取分类统计数据
    """
    analyzer = BasicAnalyzer()
    category_stats = analyzer.get_category_stats()
    
    # 转换为前端所需格式
    result = {
        'labels': [stat['name'] for stat in category_stats],
        'data': [stat['product_count'] for stat in category_stats]
    }
    
    return jsonify(result)


@analysis_bp.route('/api/price_distribution')
def api_price_distribution():
    """
    价格分布API
    用于AJAX请求获取价格分布数据
    """
    analyzer = BasicAnalyzer()
    price_distribution = analyzer.get_price_distribution()
    
    # 转换为前端所需格式
    result = {
        'labels': [dist['range'] for dist in price_distribution],
        'data': [dist['count'] for dist in price_distribution]
    }
    
    return jsonify(result)


@analysis_bp.route('/api/sales_distribution')
def api_sales_distribution():
    """
    销量分布API
    用于AJAX请求获取销量分布数据
    """
    analyzer = BasicAnalyzer()
    sales_distribution = analyzer.get_sales_distribution()
    
    # 转换为前端所需格式
    result = {
        'labels': [dist['range'] for dist in sales_distribution],
        'data': [dist['count'] for dist in sales_distribution]
    }
    
    return jsonify(result)


@analysis_bp.route('/api/price_trend')
def api_price_trend():
    """
    价格趋势API
    用于AJAX请求获取价格趋势数据
    """
    # 获取分类ID参数
    category_id = request.args.get('category_id', None, type=int)
    
    analyzer = AdvancedAnalyzer()
    price_trend = analyzer.analyze_price_trends(category_id=category_id)
    
    return jsonify(price_trend)


@analysis_bp.route('/api/keyword_cloud')
def api_keyword_cloud():
    """
    关键词云API
    用于AJAX请求获取关键词云数据
    """
    # 获取分类ID参数
    category_id = request.args.get('category_id', None, type=int)
    
    analyzer = TextAnalyzer()
    keyword_stats = analyzer.get_keyword_stats(category_id=category_id)
    
    # 转换为词云所需格式
    result = [{'text': keyword, 'weight': count} for keyword, count in keyword_stats.items()]
    
    return jsonify(result)