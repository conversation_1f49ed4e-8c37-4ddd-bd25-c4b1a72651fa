# -*- coding: utf-8 -*-
"""
数据库模型，定义产品数据的表结构
"""

import json
from datetime import datetime
from sqlalchemy import Column, Integer, String, Float, Text, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship

from .db_connection import Base

class Category(Base):
    """
    产品分类表
    """
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(50), nullable=False, unique=True, comment="分类名称")
    description = Column(String(255), nullable=True, comment="分类描述")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 关联关系
    products = relationship("Product", back_populates="category")
    
    def __repr__(self):
        return f"<Category(id={self.id}, name='{self.name}')>"

class Product(Base):
    """
    产品表
    """
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(String(50), nullable=False, unique=True, comment="产品ID")
    title = Column(String(255), nullable=False, comment="产品标题")
    price = Column(Float, nullable=True, comment="产品价格")
    original_price = Column(Float, nullable=True, comment="原价")
    sales = Column(Integer, nullable=True, comment="销量")
    detail_url = Column(String(255), nullable=True, comment="详情页URL")
    image_url = Column(String(255), nullable=True, comment="图片URL")
    rating = Column(Float, nullable=True, comment="评分")
    review_count = Column(Integer, nullable=True, comment="评论数")
    location = Column(String(50), nullable=True, comment="发货地")
    keyword = Column(String(50), nullable=True, comment="搜索关键词")
    attributes = Column(JSON, nullable=True, comment="产品属性")
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True, comment="分类ID")
    shop_id = Column(Integer, ForeignKey("shops.id"), nullable=True, comment="店铺ID")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    crawl_time = Column(DateTime, nullable=True, comment="爬取时间")
    
    # 关联关系
    category = relationship("Category", back_populates="products")
    shop = relationship("Shop", back_populates="products")
    
    def __repr__(self):
        return f"<Product(id={self.id}, title='{self.title[:20]}...')>"

class Shop(Base):
    """
    店铺表
    """
    __tablename__ = "shops"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    shop_id = Column(String(50), nullable=True, unique=True, comment="店铺ID")
    name = Column(String(100), nullable=False, comment="店铺名称")
    url = Column(String(255), nullable=True, comment="店铺URL")
    rating = Column(Float, nullable=True, comment="店铺评分")
    product_count = Column(Integer, nullable=True, comment="产品数量")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    # 关联关系
    products = relationship("Product", back_populates="shop")
    
    def __repr__(self):
        return f"<Shop(id={self.id}, name='{self.name}')>"

class PriceHistory(Base):
    """
    价格历史表
    """
    __tablename__ = "price_history"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(String(50), nullable=False, comment="产品ID")
    price = Column(Float, nullable=False, comment="价格")
    date = Column(DateTime, default=datetime.now, comment="记录日期")
    
    def __repr__(self):
        return f"<PriceHistory(product_id='{self.product_id}', price={self.price}, date='{self.date}')>"

class SalesHistory(Base):
    """
    销量历史表
    """
    __tablename__ = "sales_history"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(String(50), nullable=False, comment="产品ID")
    sales = Column(Integer, nullable=False, comment="销量")
    date = Column(DateTime, default=datetime.now, comment="记录日期")
    
    def __repr__(self):
        return f"<SalesHistory(product_id='{self.product_id}', sales={self.sales}, date='{self.date}')>"

class RawData(Base):
    """
    原始数据表，用于存储爬取的原始JSON数据
    """
    __tablename__ = "raw_data"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    product_id = Column(String(50), nullable=False, comment="产品ID")
    data = Column(Text, nullable=False, comment="原始JSON数据")
    crawl_time = Column(DateTime, default=datetime.now, comment="爬取时间")
    
    def __repr__(self):
        return f"<RawData(id={self.id}, product_id='{self.product_id}')>"
    
    def get_data_dict(self):
        """
        获取JSON数据的字典形式
        
        Returns:
            dict: 数据字典
        """
        return json.loads(self.data)