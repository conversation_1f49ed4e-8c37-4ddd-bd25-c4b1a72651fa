# -*- coding: utf-8 -*-
"""
重新初始化数据库并导入数据的脚本
"""

import os
import sys
import logging
import argparse
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("reinitialize_and_import.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def parse_args():
    """
    解析命令行参数
    """
    parser = argparse.ArgumentParser(description="重新初始化数据库并导入数据")
    parser.add_argument(
        "--categories", 
        type=str, 
        help="要生成的分类，多个分类用逗号分隔，不指定则生成所有分类"
    )
    parser.add_argument(
        "--count", 
        type=int, 
        default=50, 
        help="每个关键词生成的产品数量，默认为50"
    )
    parser.add_argument(
        "--skip-init", 
        action="store_true", 
        help="跳过数据库初始化步骤"
    )
    parser.add_argument(
        "--skip-generate", 
        action="store_true", 
        help="跳过数据生成步骤"
    )
    return parser.parse_args()

def reinitialize_database():
    """
    重新初始化数据库
    """
    from data.init_db import main as init_db_main
    
    logger.info("开始重新初始化数据库...")
    result = init_db_main()
    if result:
        logger.info("数据库重新初始化成功")
    else:
        logger.error("数据库重新初始化失败")
    return result

def generate_data(categories=None, count_per_keyword=50):
    """
    生成模拟数据
    
    Args:
        categories: 要生成的分类列表，None表示生成所有分类
        count_per_keyword: 每个关键词生成的产品数量
    
    Returns:
        list: 生成的数据文件路径列表
    """
    from examples.data_generator import DataGenerator, generate_and_save, generate_all_categories
    from crawler.config import ELECTRONIC_CATEGORIES
    
    logger.info("开始生成模拟数据...")
    
    # 生成数据
    if categories:
        # 生成指定分类的数据
        file_paths = []
        for category_name in categories:
            logger.info(f"生成分类 {category_name} 的数据")
            file_path = generate_and_save(category_name, count_per_keyword)
            if file_path:
                file_paths.append(file_path)
        return file_paths
    else:
        # 生成所有分类的数据
        logger.info("生成所有分类的数据")
        return generate_all_categories(count_per_keyword)

def import_data(file_paths):
    """
    导入数据到数据库
    
    Args:
        file_paths: 数据文件路径列表
    
    Returns:
        tuple: (成功导入数量, 失败数量)
    """
    from data.data_import import DataImporter
    
    logger.info("开始导入数据到数据库...")
    
    # 创建数据导入器
    importer = DataImporter()
    
    # 导入数据
    total_success = 0
    total_failed = 0
    
    try:
        for file_path in file_paths:
            # 从文件名中提取分类名称
            file_name = os.path.basename(file_path)
            category_name = file_name.split('_')[0] if '_' in file_name else None
            
            logger.info(f"开始导入文件 {file_path} 到数据库，分类: {category_name}")
            
            # 导入数据
            success, failed = importer.import_from_file(file_path)
            total_success += success
            total_failed += failed
        
        logger.info(f"数据导入完成，成功导入 {total_success} 条产品数据，失败 {total_failed} 条")
        return total_success, total_failed
    except Exception as e:
        logger.error(f"导入数据失败: {str(e)}")
        return total_success, total_failed
    finally:
        importer.close()

def main():
    """
    主函数
    """
    # 解析命令行参数
    args = parse_args()
    
    # 重新初始化数据库
    if not args.skip_init:
        if not reinitialize_database():
            logger.error("数据库重新初始化失败，程序终止")
            return False
    
    # 生成模拟数据
    if not args.skip_generate:
        categories = args.categories.split(',') if args.categories else None
        file_paths = generate_data(categories, args.count)
    else:
        # 如果跳过生成步骤，则使用已有的数据文件
        raw_data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'raw')
        file_paths = [os.path.join(raw_data_dir, f) for f in os.listdir(raw_data_dir) if f.endswith('.json')]
    
    # 导入数据到数据库
    success, failed = import_data(file_paths)
    
    if success > 0:
        logger.info("数据重新初始化和导入完成")
        logger.info("现在您可以运行数据分析和Web仪表盘：")
        logger.info("1. 运行数据分析: python -m examples.data_analysis_example")
        logger.info("2. 启动Web仪表盘: python run_dashboard.py")
        return True
    else:
        logger.error("没有成功导入任何数据，请检查错误日志")
        return False

if __name__ == "__main__":
    main()