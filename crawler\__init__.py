# -*- coding: utf-8 -*-
"""
爬虫模块初始化文件

该模块提供了用于爬取电商平台（主要是淘宝）电子产品数据的功能。
主要组件包括：
- TaobaoCrawler: 淘宝爬虫类，用于爬取淘宝电子产品数据
- BaseCrawler: 爬虫基类，提供通用的爬虫功能
- UserBehavior: 用户行为模拟类，用于模拟人类操作浏览器
- 配置模块: 提供爬虫配置信息
- 主程序: 用于启动爬虫并保存数据
"""

# 导出主要组件
from .taobao_crawler import TaobaoCrawler
from .base_crawler import BaseCrawler
from .user_behavior import UserBehavior
from .config import ELECTRONIC_CATEGORIES, PAGES_PER_KEYWORD, GET_DETAILS, MAX_PRODUCTS_PER_CATEGORY
from .main import main, save_to_json

__all__ = [
    'TaobaoCrawler',
    'BaseCrawler',
    'UserBehavior',
    'ELECTRONIC_CATEGORIES',
    'PAGES_PER_KEYWORD',
    'GET_DETAILS',
    'MAX_PRODUCTS_PER_CATEGORY',
    'main',
    'save_to_json'
]