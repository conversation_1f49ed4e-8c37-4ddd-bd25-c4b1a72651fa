# -*- coding: utf-8 -*-
"""
项目初始化脚本

该脚本用于初始化项目环境和数据库
"""

import os
import sys
import logging
import argparse
from dotenv import load_dotenv

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("setup.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def check_environment():
    """
    检查环境变量和依赖
    """
    # 检查.env文件
    env_file = os.path.join(os.path.dirname(__file__), ".env")
    if not os.path.exists(env_file):
        logger.warning(".env文件不存在，创建示例文件")
        create_env_file(env_file)
    else:
        logger.info(".env文件已存在")
    
    # 加载环境变量
    load_dotenv(env_file)
    
    # 检查必要的环境变量
    required_vars = ["DB_HOST", "DB_PORT", "DB_USER", "DB_PASSWORD", "DB_NAME"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.warning(f"缺少必要的环境变量: {', '.join(missing_vars)}")
        logger.warning(f"请在.env文件中设置这些变量: {env_file}")
        return False
    
    return True

def create_env_file(file_path):
    """
    创建示例.env文件
    
    Args:
        file_path: .env文件路径
    """
    env_content = """
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=ecommerce_data

# 淘宝账号配置（可选）
# TAOBAO_USERNAME=your_username
# TAOBAO_PASSWORD=your_password
"""
    
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(env_content)
    
    logger.info(f"已创建示例.env文件: {file_path}")

def init_database():
    """
    初始化数据库
    """
    try:
        from data.db_connection import init_db
        
        # 初始化数据库
        init_db()
        logger.info("数据库初始化成功")
        return True
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        return False

def create_directories():
    """
    创建必要的目录
    """
    # 创建数据目录
    dirs = [
        "data/raw",  # 原始数据
        "data/processed",  # 处理后的数据
        "data/visualization",  # 可视化结果
        "data/visualization/dashboard",  # 仪表盘图表
        "logs",  # 日志
        "templates"  # Web模板
    ]
    
    for dir_path in dirs:
        full_path = os.path.join(os.path.dirname(__file__), dir_path)
        if not os.path.exists(full_path):
            os.makedirs(full_path, exist_ok=True)
            logger.info(f"已创建目录: {full_path}")

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="项目初始化脚本")
    parser.add_argument("--skip-db", action="store_true", help="跳过数据库初始化")
    parser.add_argument("--force-env", action="store_true", help="强制创建新的.env文件")
    
    args = parser.parse_args()
    
    # 如果强制创建.env文件
    if args.force_env:
        env_file = os.path.join(os.path.dirname(__file__), ".env")
        create_env_file(env_file)
    
    # 检查环境
    env_ok = check_environment()
    if not env_ok:
        logger.warning("环境检查未通过，请检查.env文件")
        return
    
    # 创建目录
    create_directories()
    
    # 初始化数据库
    if not args.skip_db:
        db_ok = init_database()
        if not db_ok:
            logger.warning("数据库初始化失败，请检查数据库配置")
            return
    
    logger.info("项目初始化完成")
    print("\n项目初始化完成！")
    print("\n下一步:")
    print("1. 确保已安装所有依赖: pip install -r requirements.txt")
    print("2. 运行爬虫示例: python examples/crawler_example.py --help")
    print("3. 运行Web仪表盘: python examples/web_dashboard.py")

if __name__ == "__main__":
    main()