# -*- coding: utf-8 -*-
"""
数据分析示例脚本

该脚本展示如何使用数据分析模块分析电商数据
"""

import os
import sys
import logging
import json
import decimal
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import pandas as pd

# 添加项目根目录到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 导入数据分析模块
from data.data_analysis import get_analyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("data_analysis.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def save_to_json(data, filename):
    """
    将数据保存为JSON文件
    
    Args:
        data: 要保存的数据
        filename: 文件名
    """
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/analysis")
    os.makedirs(output_dir, exist_ok=True)
    
    # 自定义JSON编码器，处理Decimal类型
    class DecimalEncoder(json.JSONEncoder):
        def default(self, obj):
            if isinstance(obj, decimal.Decimal):
                return float(obj)
            return super(DecimalEncoder, self).default(obj)
    
    # 保存数据
    filepath = os.path.join(output_dir, filename)
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2, cls=DecimalEncoder)
    
    logger.info(f"数据已保存到 {filepath}")

def plot_price_distribution(category_name=None):
    """
    绘制价格分布图
    
    Args:
        category_name: 分类名称，如果为None则获取所有分类
    """
    analyzer = get_analyzer()
    
    # 获取价格区间分布
    distribution = analyzer.get_price_range_distribution(category_name)
    
    if not distribution:
        logger.warning("未获取到价格分布数据")
        return
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/analysis/plots")
    os.makedirs(output_dir, exist_ok=True)
    
    # 绘制柱状图
    plt.figure(figsize=(10, 6))
    plt.bar(distribution.keys(), distribution.values())
    plt.title(f"价格分布 - {category_name if category_name else '所有分类'}")
    plt.xlabel("价格区间")
    plt.ylabel("产品数量")
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # 保存图片
    category_str = category_name.replace(' ', '_') if category_name else 'all'
    filename = f"price_distribution_{category_str}.png"
    filepath = os.path.join(output_dir, filename)
    plt.savefig(filepath)
    
    logger.info(f"价格分布图已保存到 {filepath}")
    
    # 保存数据
    save_to_json(distribution, f"price_distribution_{category_str}.json")

def plot_price_trend(category_name=None, days=30):
    """
    绘制价格趋势图
    
    Args:
        category_name: 分类名称，如果为None则获取所有分类
        days: 天数
    """
    analyzer = get_analyzer()
    
    # 获取价格趋势
    trend = analyzer.get_price_trend(category_name=category_name, days=days)
    
    if not trend:
        logger.warning("未获取到价格趋势数据")
        return
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/analysis/plots")
    os.makedirs(output_dir, exist_ok=True)
    
    # 转换为DataFrame
    df = pd.DataFrame(list(trend.items()), columns=['date', 'price'])
    df['date'] = pd.to_datetime(df['date'])
    df = df.sort_values('date')
    
    # 绘制折线图
    plt.figure(figsize=(12, 6))
    plt.plot(df['date'], df['price'], marker='o')
    plt.title(f"价格趋势 - {category_name if category_name else '所有分类'} (过去{days}天)")
    plt.xlabel("日期")
    plt.ylabel("平均价格")
    plt.grid(True)
    plt.tight_layout()
    
    # 保存图片
    category_str = category_name.replace(' ', '_') if category_name else 'all'
    filename = f"price_trend_{category_str}_{days}days.png"
    filepath = os.path.join(output_dir, filename)
    plt.savefig(filepath)
    
    logger.info(f"价格趋势图已保存到 {filepath}")
    
    # 保存数据
    save_to_json(trend, f"price_trend_{category_str}_{days}days.json")

def analyze_category_stats():
    """
    分析分类统计信息
    """
    analyzer = get_analyzer()
    
    # 获取分类统计信息
    stats = analyzer.get_category_stats()
    
    if not stats:
        logger.warning("未获取到分类统计信息")
        return
    
    # 保存数据
    save_to_json(stats, "category_stats.json")
    
    # 创建输出目录
    output_dir = os.path.join(os.path.dirname(__file__), "../data/analysis/plots")
    os.makedirs(output_dir, exist_ok=True)
    
    # 转换为DataFrame
    df = pd.DataFrame(stats)
    
    # 绘制产品数量柱状图
    plt.figure(figsize=(12, 6))
    plt.bar(df['name'], df['product_count'])
    plt.title("各分类产品数量")
    plt.xlabel("分类")
    plt.ylabel("产品数量")
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # 保存图片
    filepath = os.path.join(output_dir, "category_product_count.png")
    plt.savefig(filepath)
    
    logger.info(f"分类产品数量图已保存到 {filepath}")
    
    # 绘制平均价格柱状图
    plt.figure(figsize=(12, 6))
    plt.bar(df['name'], df['avg_price'])
    plt.title("各分类平均价格")
    plt.xlabel("分类")
    plt.ylabel("平均价格")
    plt.xticks(rotation=45)
    plt.tight_layout()
    
    # 保存图片
    filepath = os.path.join(output_dir, "category_avg_price.png")
    plt.savefig(filepath)
    
    logger.info(f"分类平均价格图已保存到 {filepath}")

def analyze_top_products(category_name=None, limit=10):
    """
    分析热门产品
    
    Args:
        category_name: 分类名称，如果为None则获取所有分类
        limit: 返回的产品数量
    """
    analyzer = get_analyzer()
    
    # 获取热门产品
    products = analyzer.get_top_products(category_name=category_name, limit=limit)
    
    if not products:
        logger.warning("未获取到热门产品数据")
        return
    
    # 保存数据
    category_str = category_name.replace(' ', '_') if category_name else 'all'
    save_to_json(products, f"top_products_{category_str}.json")
    
    logger.info(f"已获取{len(products)}个热门产品")
    
    # 打印热门产品信息
    print(f"\n热门产品 - {category_name if category_name else '所有分类'}:")
    for i, product in enumerate(products, 1):
        print(f"{i}. {product['title']} - ¥{product['price']} - 销量: {product['sales']} - 评分: {product['rating']}")

def analyze_summary_stats(category_name=None):
    """
    分析汇总统计信息
    
    Args:
        category_name: 分类名称，如果为None则获取所有分类
    """
    analyzer = get_analyzer()
    
    # 获取汇总统计信息
    stats = analyzer.get_summary_stats(category_name)
    
    if not stats:
        logger.warning("未获取到汇总统计信息")
        return
    
    # 保存数据
    category_str = category_name.replace(' ', '_') if category_name else 'all'
    save_to_json(stats, f"summary_stats_{category_str}.json")
    
    # 打印汇总统计信息
    print(f"\n汇总统计信息 - {category_name if category_name else '所有分类'}:")
    print(f"产品总数: {stats['product_count']}")
    print(f"平均价格: ¥{stats['avg_price']}")
    print(f"价格范围: ¥{stats['min_price']} - ¥{stats['max_price']}")
    print(f"平均销量: {stats['avg_sales']}")
    print(f"总销量: {stats['total_sales']}")
    print(f"平均评分: {stats['avg_rating']}")
    print(f"店铺数量: {stats['shop_count']}")
    print(f"30天增长率: {stats['growth_rate']}%")

def main():
    """
    主函数
    """
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="淘宝电子产品数据分析工具")
    parser.add_argument("--category", help="要分析的分类名称")
    parser.add_argument("--days", type=int, default=30, help="趋势分析的天数")
    parser.add_argument("--limit", type=int, default=10, help="返回的结果数量")
    parser.add_argument("--all", action="store_true", help="运行所有分析")
    parser.add_argument("--price-distribution", action="store_true", help="分析价格分布")
    parser.add_argument("--price-trend", action="store_true", help="分析价格趋势")
    parser.add_argument("--category-stats", action="store_true", help="分析分类统计信息")
    parser.add_argument("--top-products", action="store_true", help="分析热门产品")
    parser.add_argument("--summary", action="store_true", help="分析汇总统计信息")
    
    args = parser.parse_args()
    
    # 如果没有指定任何分析，则默认运行汇总分析
    if not (args.all or args.price_distribution or args.price_trend or 
            args.category_stats or args.top_products or args.summary):
        args.summary = True
    
    try:
        # 运行指定的分析
        if args.all or args.price_distribution:
            plot_price_distribution(args.category)
        
        if args.all or args.price_trend:
            plot_price_trend(args.category, args.days)
        
        if args.all or args.category_stats:
            analyze_category_stats()
        
        if args.all or args.top_products:
            analyze_top_products(args.category, args.limit)
        
        if args.all or args.summary:
            analyze_summary_stats(args.category)
        
        logger.info("数据分析完成")
        
    except Exception as e:
        logger.error(f"数据分析过程中发生错误: {str(e)}")
        raise

if __name__ == "__main__":
    main()